# sf-sensechoice

A customizable, embeddable Choice Based Conjoint (CBC) survey web component that enables sophisticated preference research through multi-attribute choice tasks.

## Overview

The `sf-sensechoice` component provides a comprehensive Choice Based Conjoint analysis experience that can be embedded on any website. It presents users with multiple choice tasks where they select their preferred option from alternatives defined by various attributes and levels.

## Installation

### NPM

```bash
npm install @sensefolks/sf-sensechoice --save
```

### Script Tag

```html
<script type="module" src="https://unpkg.com/@sensefolks/sf-sensechoice/dist/sf-sensechoice/sf-sensechoice.esm.js"></script>
<script nomodule src="https://unpkg.com/@sensefolks/sf-sensechoice/dist/sf-sensechoice/sf-sensechoice.js"></script>
```

## Basic Usage

Add the component to your HTML with a survey key:

```html
<sf-sensechoice survey-key="your-survey-key"></sf-sensechoice>
```

## Features

- **Choice Based Conjoint**: Full CBC analysis with multiple attributes and levels
- **Multi-Task Flow**: Sequential choice tasks with progress tracking
- **Flexible Concepts**: Support for user-defined product/service concepts
- **None Option**: Optional "none of these" choice for realistic scenarios
- **Attribute Display**: Clear presentation of attribute-level combinations
- **Progress Tracking**: Visual progress indicator across choice tasks
- **Task Navigation**: Forward/backward navigation between tasks
- **Respondent Details**: Optional collection of respondent information
- **Response Validation**: Ensures all tasks are completed before submission
- **Error Handling**: Graceful error handling with retry functionality
- **Framework Agnostic**: Works with React, Vue, Angular, and vanilla JavaScript

## Configuration

The component expects a survey configuration from the API with the following structure:

```typescript
interface ConjointConcept {
  conceptId: string; // Unique identifier for the concept
  attributes: { [key: string]: string }; // attributeValue -> selected variant value
}

interface ConjointChoiceTask {
  taskId: string; // Unique identifier for the task
  taskNumber: number; // Display number for the task
  alternatives: ConjointConcept[]; // Array of concepts to choose from
  includeNoneOption: boolean; // Whether to include "none of these" option
}

interface SurveyConfig {
  type: string; // 'lite' or 'full'
  attributes: { label: string; value: string }[]; // Available attributes
  attributeVariants: { [key: string]: { label: string; value: string }[] }; // Variants for each attribute
  selectedConcepts: ConjointConcept[]; // User-curated concepts
  choiceTasks: ConjointChoiceTask[]; // User-curated choice tasks
  thankYouMessage: string; // Thank you message
}
```

## Styling

The component is completely unstyled by default and uses CSS parts for customization.

### Example Styling

```css
/* Main container styling */
sf-sensechoice {
  display: block;
  font-family: system-ui, -apple-system, sans-serif;
  max-width: 900px;
  margin: 0 auto;
  color: #333;
}

/* Task header */
sf-sensechoice::part(task-header) {
  text-align: center;
  margin-bottom: 2rem;
}

sf-sensechoice::part(task-heading) {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

sf-sensechoice::part(task-instructions) {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

/* Concepts container */
sf-sensechoice::part(concepts-container) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

/* Individual concepts */
sf-sensechoice::part(concept) {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #ffffff;
}

sf-sensechoice::part(concept):hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

sf-sensechoice::part(concept-selected) {
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
}

sf-sensechoice::part(concept-header) {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

sf-sensechoice::part(concept-radio) {
  margin-right: 0.75rem;
  transform: scale(1.2);
}

sf-sensechoice::part(concept-title) {
  font-weight: 600;
  font-size: 1.125rem;
  color: #1f2937;
}

/* Concept attributes */
sf-sensechoice::part(concept-attributes) {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

sf-sensechoice::part(concept-attribute) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

sf-sensechoice::part(concept-attribute):last-child {
  border-bottom: none;
}

sf-sensechoice::part(attribute-label) {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

sf-sensechoice::part(attribute-value) {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

/* None option styling */
sf-sensechoice::part(none-option) {
  border-style: dashed;
  background-color: #f9fafb;
}

sf-sensechoice::part(none-option):hover {
  background-color: #f3f4f6;
}

/* Progress indicator */
sf-sensechoice::part(progress-indicator) {
  margin-top: 2rem;
  text-align: center;
}

sf-sensechoice::part(progress-bar) {
  width: 100%;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

sf-sensechoice::part(progress-fill) {
  height: 100%;
  background-color: #3b82f6;
  transition: width 0.3s ease;
}

sf-sensechoice::part(progress-text) {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* Completion summary */
sf-sensechoice::part(completion-summary) {
  background-color: #ecfdf5;
  border: 1px solid #d1fae5;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1.5rem;
  text-align: center;
}

sf-sensechoice::part(summary-text) {
  color: #065f46;
  font-weight: 500;
  margin: 0;
}

/* Buttons */
sf-sensechoice::part(button) {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

sf-sensechoice::part(next-button),
sf-sensechoice::part(submit-button) {
  background-color: #3b82f6;
  color: white;
  border: none;
}

sf-sensechoice::part(next-button):hover,
sf-sensechoice::part(submit-button):hover {
  background-color: #2563eb;
}

sf-sensechoice::part(next-button):disabled,
sf-sensechoice::part(submit-button):disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

sf-sensechoice::part(back-button) {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  margin-right: 0.75rem;
}

sf-sensechoice::part(back-button):hover {
  background-color: #e5e7eb;
}

/* Button container */
sf-sensechoice::part(button-container) {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
  gap: 0.75rem;
}

/* Responsive design */
@media (max-width: 768px) {
  sf-sensechoice::part(concepts-container) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  sf-sensechoice::part(concept) {
    padding: 1rem;
  }

  sf-sensechoice::part(button-container) {
    flex-direction: column;
    gap: 0.5rem;
  }

  sf-sensechoice::part(button) {
    width: 100%;
  }
}
```

## Framework Integration

### React

```jsx
import React from 'react';
import '@sensefolks/sf-sensechoice';

function ChoiceComponent() {
  return <sf-sensechoice survey-key="your-survey-key"></sf-sensechoice>;
}
```

### Vue

```html
<template>
  <sf-sensechoice survey-key="your-survey-key"></sf-sensechoice>
</template>

<script>
  import '@sensefolks/sf-sensechoice';

  export default {
    name: 'ChoiceComponent',
  };
</script>
```

### Angular

```typescript
// In your module
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {}

// In your component
import '@sensefolks/sf-sensechoice';
```

<!-- Auto Generated Below -->


## Properties

| Property    | Attribute    | Description | Type     | Default     |
| ----------- | ------------ | ----------- | -------- | ----------- |
| `surveyKey` | `survey-key` |             | `string` | `undefined` |


## Shadow Parts

| Part                           | Description |
| ------------------------------ | ----------- |
| `"attribute-label"`            |             |
| `"attribute-value"`            |             |
| `"back-button"`                |             |
| `"button"`                     |             |
| `"button-container"`           |             |
| `"choice-task-step"`           |             |
| `"completion-summary"`         |             |
| `"concept-attribute"`          |             |
| `"concept-attributes"`         |             |
| `"concept-header"`             |             |
| `"concept-radio"`              |             |
| `"concept-title"`              |             |
| `"concepts-container"`         |             |
| `"error-container"`            |             |
| `"error-message"`              |             |
| `"form-container"`             |             |
| `"form-field"`                 |             |
| `"form-input"`                 |             |
| `"form-label"`                 |             |
| `"heading"`                    |             |
| `"input"`                      |             |
| `"loading-message"`            |             |
| `"message"`                    |             |
| `"next-button"`                |             |
| `"progress-bar"`               |             |
| `"progress-fill"`              |             |
| `"progress-indicator"`         |             |
| `"progress-text"`              |             |
| `"required-indicator"`         |             |
| `"respondent-details-heading"` |             |
| `"respondent-details-step"`    |             |
| `"retry-button"`               |             |
| `"step"`                       |             |
| `"submit-button"`              |             |
| `"summary-text"`               |             |
| `"survey-container"`           |             |
| `"task-header"`                |             |
| `"task-heading"`               |             |
| `"task-instructions"`          |             |
| `"thank-you-heading"`          |             |
| `"thank-you-step"`             |             |


----------------------------------------------

*Built with [StencilJS](https://stenciljs.com/)*
