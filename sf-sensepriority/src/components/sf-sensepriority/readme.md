# sf-sensepriority

A customizable, embeddable priority ranking web component that allows users to rank items by importance through an intuitive drag-and-drop interface.

## Overview

The `sf-sensepriority` component provides an interactive priority ranking experience that can be embedded on any website. Users can reorder items using drag-and-drop or arrow buttons to indicate their priority preferences.

## Installation

### NPM

```bash
npm install @sensefolks/sf-sensepriority --save
```

### Script Tag

```html
<script type="module" src="https://unpkg.com/@sensefolks/sf-sensepriority/dist/sf-sensepriority/sf-sensepriority.esm.js"></script>
<script nomodule src="https://unpkg.com/@sensefolks/sf-sensepriority/dist/sf-sensepriority/sf-sensepriority.js"></script>
```

## Basic Usage

Add the component to your HTML with a survey key:

```html
<sf-sensepriority survey-key="your-survey-key"></sf-sensepriority>
```

## Features

- **Drag & Drop**: Intuitive drag-and-drop interface for reordering items
- **Arrow Controls**: Alternative arrow button controls for accessibility
- **Auto Ranking**: Automatic rank numbering based on position
- **Item Shuffling**: Initial random order to avoid bias
- **Rich Content**: Support for item titles and descriptions
- **Respondent Details**: Optional collection of respondent information
- **Visual Feedback**: Clear visual indicators for dragging and ranking
- **Error Handling**: Graceful error handling with retry functionality
- **Framework Agnostic**: Works with React, Vue, Angular, and vanilla JavaScript

## Configuration

The component expects a survey configuration from the API with the following structure:

```typescript
interface PriorityItem {
  title: string; // Item title
  description?: string; // Optional item description
  value: string; // Unique identifier for the item
}

interface SurveyConfig {
  question: string; // Priority ranking question
  items: PriorityItem[]; // Array of items to rank
  thankYouMessage: string; // Thank you message
}
```

## Styling

The component is completely unstyled by default and uses CSS parts for customization.

### Example Styling

```css
/* Main container styling */
sf-sensepriority {
  display: block;
  font-family: system-ui, -apple-system, sans-serif;
  max-width: 800px;
  margin: 0 auto;
  color: #333;
}

/* Instructions */
sf-sensepriority::part(instructions) {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Ranking container */
sf-sensepriority::part(ranking-container) {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin: 1.5rem 0;
}

/* Individual ranking items */
sf-sensepriority::part(ranking-item) {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  cursor: grab;
  transition: all 0.2s;
}

sf-sensepriority::part(ranking-item):hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

sf-sensepriority::part(ranking-item):active {
  cursor: grabbing;
}

/* Rank number */
sf-sensepriority::part(rank-number) {
  background-color: #3b82f6;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 1rem;
  flex-shrink: 0;
}

/* Item content */
sf-sensepriority::part(item-content) {
  flex: 1;
  margin-right: 1rem;
}

sf-sensepriority::part(item-title) {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

sf-sensepriority::part(item-description) {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Control buttons */
sf-sensepriority::part(item-controls) {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

sf-sensepriority::part(control-button) {
  width: 2rem;
  height: 2rem;
  border: 1px solid #d1d5db;
  background-color: #f9fafb;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s;
}

sf-sensepriority::part(control-button):hover:not(:disabled) {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

sf-sensepriority::part(control-button):disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Summary display */
sf-sensepriority::part(ranking-summary) {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

sf-sensepriority::part(summary-heading) {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1e293b;
}

sf-sensepriority::part(summary-list) {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

sf-sensepriority::part(summary-item) {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

sf-sensepriority::part(summary-rank) {
  background-color: #3b82f6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  min-width: 2rem;
  text-align: center;
}

sf-sensepriority::part(summary-title) {
  font-weight: 500;
}

/* Buttons */
sf-sensepriority::part(button) {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

sf-sensepriority::part(next-button),
sf-sensepriority::part(submit-button) {
  background-color: #3b82f6;
  color: white;
  border: none;
}

sf-sensepriority::part(next-button):hover,
sf-sensepriority::part(submit-button):hover {
  background-color: #2563eb;
}

sf-sensepriority::part(back-button) {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  margin-right: 0.75rem;
}
```

## Framework Integration

### React

```jsx
import React from 'react';
import '@sensefolks/sf-sensepriority';

function PriorityComponent() {
  return <sf-sensepriority survey-key="your-survey-key"></sf-sensepriority>;
}
```

### Vue

```html
<template>
  <sf-sensepriority survey-key="your-survey-key"></sf-sensepriority>
</template>

<script>
  import '@sensefolks/sf-sensepriority';

  export default {
    name: 'PriorityComponent',
  };
</script>
```

### Angular

```typescript
// In your module
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {}

// In your component
import '@sensefolks/sf-sensepriority';
```

<!-- Auto Generated Below -->


## Properties

| Property    | Attribute    | Description | Type     | Default     |
| ----------- | ------------ | ----------- | -------- | ----------- |
| `surveyKey` | `survey-key` |             | `string` | `undefined` |


## Shadow Parts

| Part                           | Description |
| ------------------------------ | ----------- |
| `"back-button"`                |             |
| `"button"`                     |             |
| `"button-container"`           |             |
| `"control-button"`             |             |
| `"down-button"`                |             |
| `"error-container"`            |             |
| `"error-message"`              |             |
| `"form-container"`             |             |
| `"form-field"`                 |             |
| `"form-input"`                 |             |
| `"form-label"`                 |             |
| `"heading"`                    |             |
| `"input"`                      |             |
| `"instructions"`               |             |
| `"item-content"`               |             |
| `"item-controls"`              |             |
| `"item-description"`           |             |
| `"item-title"`                 |             |
| `"loading-message"`            |             |
| `"message"`                    |             |
| `"next-button"`                |             |
| `"priority-heading"`           |             |
| `"priority-ranking-step"`      |             |
| `"rank-number"`                |             |
| `"ranking-container"`          |             |
| `"ranking-item"`               |             |
| `"ranking-summary"`            |             |
| `"required-indicator"`         |             |
| `"respondent-details-heading"` |             |
| `"respondent-details-step"`    |             |
| `"retry-button"`               |             |
| `"step"`                       |             |
| `"submit-button"`              |             |
| `"summary-heading"`            |             |
| `"summary-item"`               |             |
| `"summary-list"`               |             |
| `"summary-rank"`               |             |
| `"summary-title"`              |             |
| `"survey-container"`           |             |
| `"thank-you-heading"`          |             |
| `"thank-you-step"`             |             |
| `"up-button"`                  |             |


----------------------------------------------

*Built with [StencilJS](https://stenciljs.com/)*
