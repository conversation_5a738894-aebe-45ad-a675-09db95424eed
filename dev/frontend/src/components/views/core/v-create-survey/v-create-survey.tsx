import { Component, State, FunctionalComponent, Listen, Host, h } from '@stencil/core';
import { createSurveyPayloadInterface } from './interfaces';
import { createSurveyApi } from './helpers';
import {
  generateCreateSurveyPayload,
  generateSensePollConfig,
  generateSenseQueryConfig,
  generateSensePriorityConfig,
  generateSensePriceConfig,
  generateSenseChoiceConfig,
  validateCreateSurveyPayload,
} from './helpers';
import { ValidateUrlPayload } from '../../../../global/script/helpers';
import {
  RespondentDetailsOptions,
  FrontendLogger,
  ProductTypes,
  ServiceTypes,
  ProductIndustries,
  ServiceIndustries,
  CurrencyOptions,
} from '../../../../global/script/var';
import { Router } from '../../../..';

interface SurveyRadioProps {
  label: string;
  description: string;
  value: string;
  color: string;
}

@Component({
  tag: 'v-create-survey',
  styleUrl: 'v-create-survey.css',
  shadow: true,
})
export class VCreateSurvey {
  /* Survey Meta States */
  @State() currentStepIndex: number = 0;
  @State() isCreatingSurvey: boolean = false;
  @State() isLoading: boolean = false;
  @State() surveyConfig: any;
  @State() isCustomDetailModalOpen: boolean = false;
  @State() isEditMode: boolean = false;
  @State() editingDetail: any = null;
  @State() editingIndex: number = -1;
  @State() resetRespondentDetailsSelectTrigger: number = 0;
  @State()
  surveySteps: any = [
    { label: 'Choose Survey Type' },
    { label: 'Basic Information' },
    { label: 'Configuration (1)' },
    { label: 'Configuration (2)' },
    { label: 'Respondent Details' },
    { label: 'Thank You Message' },
    { label: 'Summary' },
  ];

  /* Survey Basics States */
  @State() surveyType: string = '';
  @State() surveyTitle: string = '';
  @State() surveyDescription: string = '';
  @State() surveyDistributionMode: string = '';
  @State() isEmbedMode: boolean = false;
  @State() surveyEmbedUrl: string = '';
  @State() surveyTags: string[] = [];
  @State() surveyThankYouMessage: string = 'Thank you for your response!';

  /* SensePoll Config States */
  @State() sensePollQuestion: string = '';
  @State() sensePollChoiceType: string = '';
  @State() sensePollChoices: { label: string; value: string }[] = [];
  @State() sensePollFollowUpChoices: any = [];
  @State() sensePollFollowUpQuestion: string = '';
  @State() isPollChoiceModalOpen: boolean = false;

  /* SenseQuery Config States */
  @State() senseQueryQuestion: string = '';
  @State() senseQueryCategories: { label: string; value: string }[] = [];
  @State() senseQueryCategory: string = '';
  @State() isCategoryModalOpen: boolean = false;
  @State() isCategoryInfoModalOpen: boolean = false;

  /* SensePriority Config States */
  @State() sensePriorityQuestion: string = '';
  @State() sensePriorityItems: { title: string; value: string }[] = [];
  @State() sensePriorityMaxPriorities: number = 3;
  @State() isPriorityItemModalOpen: boolean = false;
  @State() isPriorityItemInfoModalOpen: boolean = false;
  @State() isEditingPriorityItem: boolean = false;
  @State() editingPriorityItem: { title: string; value: string } | null = null;
  @State() editingPriorityItemIndex: number = -1;

  /* SenseChoice Config States */
  @State() senseChoiceType: string = ''; // 'lite' or 'full'
  @State() senseChoiceAttributes: any[] = [];
  @State() senseChoiceAttributeVariants: { [key: string]: { label: string; value: string }[] } = {}; // attributeValue -> variants array
  @State() senseChoiceConfigStep: number = 1; // 1 = type/attributes, 2 = variants, 3 = concepts, 4 = tasks
  @State() senseChoiceGeneratedConcepts: any[] = [];
  @State() senseChoiceSelectedConcepts: any[] = [];
  @State() senseChoiceGeneratedTasks: any[] = [];
  @State() senseChoiceSelectedTasks: any[] = [];
  @State() isAttributeModalOpen: boolean = false;
  @State() isVariantModalOpen: boolean = false;
  @State() isVariantInfoModalOpen: boolean = false;
  @State() currentAttributeForVariants: string = '';

  /* SensePrice Config States */
  @State() sensePriceCurrency: string = '';
  @State() sensePricePriceType: string = '';
  @State() sensePriceRecurringBasis: string = '';
  @State() sensePriceCurrencyPreference: string = '';

  /* Product/Service Configuration States - for SensePrice, SenseChoice, SensePriority */
  @State() productServiceType: string = ''; // 'product' or 'service'
  @State() productType: string = '';
  @State() serviceType: string = '';
  @State() productIndustry: string = '';
  @State() serviceIndustry: string = '';
  @State() productServiceName: string = '';
  @State() productServiceDescription: string = '';
  @State() productTypeResetTrigger: number = 0;
  @State() serviceTypeResetTrigger: number = 0;
  @State() productIndustryResetTrigger: number = 0;
  @State() serviceIndustryResetTrigger: number = 0;

  /* Survey Respondent States */
  @State() surveyRespondentDetails: Array<{
    label: string;
    value: string;
    inputType: string;
    placeholder?: string;
    options?: Array<{ label: string; value: string }>;
    required?: boolean;
    defaultValue?: string;
  }> = [];

  private surveyTitlePlaceholder = {
    sensePrice: 'e.g. Price Survey for Berrito Fizz',
    senseChoice: 'e.g. Feature Survey for EVStation Map',
    sensePoll: 'e.g. Payment Experience Poll',
    senseQuery: 'e.g. Landing Page FAQ Feedback',
    sensePriority: 'e.g. Feature Priority Survey',
  };

  private surveyEmbedPlaceholder = {
    sensePrice: 'e.g. https://example.com or https://example.com/pricing',
    senseChoice: 'e.g. https://example.com or https://example.com/charger-map',
    sensePoll: 'e.g. https://example.com or https://example.com/confirmation',
    senseQuery: 'e.g. https://example.com or https://example.com/help',
    sensePriority: 'e.g. https://example.com or https://example.com/features',
  };

  // Use the global RespondentDetailsOptions
  private respondentDetails = RespondentDetailsOptions;

  @Listen('buttonClickEvent') async handleButtonClickEvent(e) {
    if (e.detail.action === 'closeWizard') {
      Router.push('/');
    } else if (e.detail.action === 'nextStep') {
      if (this.currentStepIndex + 1 >= this.surveySteps.length) {
        return;
      }

      // Special logic for SenseQuery: skip Configuration (2) step
      if (this.surveyType === 'senseQuery' && this.currentStepIndex === 2) {
        // Skip step 3 (Configuration 2) and go directly to step 4 (Respondent Details)
        this.currentStepIndex = this.currentStepIndex + 2;
      }
      // Special logic for SenseChoice: handle sub-steps in Configuration (2)
      else if (this.surveyType === 'senseChoice' && this.currentStepIndex === 3) {
        if (this.senseChoiceConfigStep === 1) {
          // Move to sub-step 2 (levels)
          this.senseChoiceConfigStep = 2;
        } else if (this.senseChoiceConfigStep === 2) {
          // Generate concepts and move to sub-step 3 (concept selection)
          this.generateConceptsFromAttributesAndVariants();
          this.senseChoiceConfigStep = 3;
        } else if (this.senseChoiceConfigStep === 3) {
          // Generate tasks and move to sub-step 4 (task selection)
          this.generateTasksFromSelectedConcepts();
          this.senseChoiceConfigStep = 4;
        } else {
          // Move to next main step (Respondent Details)
          this.currentStepIndex = this.currentStepIndex + 1;
          this.senseChoiceConfigStep = 1; // Reset for future use
        }
      } else {
        this.currentStepIndex = this.currentStepIndex + 1;
      }
    } else if (e.detail.action === 'prevStep') {
      if (this.currentStepIndex <= 0) {
        return;
      }

      // Special logic for SenseQuery: skip Configuration (2) step when going back
      if (this.surveyType === 'senseQuery' && this.currentStepIndex === 4) {
        // Skip step 3 (Configuration 2) and go back to step 2 (Configuration 1)
        this.currentStepIndex = this.currentStepIndex - 2;
      }
      // Special logic for SenseChoice: handle sub-steps in Configuration (2)
      else if (this.surveyType === 'senseChoice' && this.currentStepIndex === 3) {
        if (this.senseChoiceConfigStep === 4) {
          // Go back to sub-step 3 (profile selection)
          this.senseChoiceConfigStep = 3;
        } else if (this.senseChoiceConfigStep === 3) {
          // Go back to sub-step 2 (levels)
          this.senseChoiceConfigStep = 2;
        } else if (this.senseChoiceConfigStep === 2) {
          // Go back to sub-step 1 (type/attributes)
          this.senseChoiceConfigStep = 1;
        } else {
          // Go back to previous main step (Configuration 1)
          this.currentStepIndex = this.currentStepIndex - 1;
        }
      }
      // Special logic for SenseChoice: when coming back from Respondent Details
      else if (this.surveyType === 'senseChoice' && this.currentStepIndex === 4) {
        // Go back to Configuration (2) sub-step 4 (task selection)
        this.currentStepIndex = 3;
        this.senseChoiceConfigStep = 4;
      } else {
        this.currentStepIndex = this.currentStepIndex - 1;
      }
    } else if (e.detail.action === 'createSurvey') {
      this.createSurvey();
    } else if (e.detail.action === 'addSensePollChoice') {
      this.isPollChoiceModalOpen = true;
    } else if (e.detail.action === 'addSenseQueryCategory') {
      this.isCategoryModalOpen = true;
    } else if (e.detail.action === 'showCategoryInfo') {
      this.isCategoryInfoModalOpen = true;
    } else if (e.detail.action === 'closeCategoryInfo') {
      this.isCategoryInfoModalOpen = false;
    } else if (e.detail.action === 'showPriorityItemInfo') {
      this.isPriorityItemInfoModalOpen = true;
    } else if (e.detail.action === 'closePriorityItemInfo') {
      this.isPriorityItemInfoModalOpen = false;
    } else if (e.detail.action === 'openVariantInfo') {
      this.isVariantInfoModalOpen = true;
    } else if (e.detail.action === 'closeVariantInfo') {
      this.isVariantInfoModalOpen = false;
    } else if (e.detail.action === 'addSensePriorityItem') {
      this.isEditingPriorityItem = false;
      this.editingPriorityItem = null;
      this.editingPriorityItemIndex = -1;
      this.isPriorityItemModalOpen = true;
    } else if (e.detail.action === 'addSenseChoiceAttribute') {
      this.isAttributeModalOpen = true;
    } else if (e.detail.action.startsWith('addVariantFor-')) {
      const attributeValue = e.detail.action.replace('addVariantFor-', '');
      this.currentAttributeForVariants = attributeValue;
      this.isVariantModalOpen = true;
    } else if (e.detail.action.startsWith('deleteRespondentDetail-')) {
      const index = parseInt(e.detail.action.split('-')[1]);
      if (index >= 0 && index < this.surveyRespondentDetails.length) {
        let updatedDetails = [];
        for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
          if (i !== index) {
            updatedDetails.push(this.surveyRespondentDetails[i]);
          }
        }
        this.surveyRespondentDetails = updatedDetails;
      }
    } else if (e.detail.action.startsWith('editRespondentDetail-')) {
      const index = parseInt(e.detail.action.split('-')[1]);
      if (index >= 0 && index < this.surveyRespondentDetails.length) {
        this.editingDetail = this.surveyRespondentDetails[index];
        this.editingIndex = index;
        this.isEditMode = true;
        this.isCustomDetailModalOpen = true;
      }
    }
  }

  @Listen('listWithDeleteEvent') async listWithDeleteEvent(e) {
    if (e.detail.name === 'deleteSensePollChoice') {
      let buff = [];
      for (let i = 0; i < this.sensePollChoices.length; i++) {
        if (this.sensePollChoices[i].value !== e.detail.value) {
          buff.push(this.sensePollChoices[i]);
        }
      }
      this.sensePollChoices = buff;
      this.sensePollChoices = [...this.sensePollChoices];

      if (this.sensePollChoices.length === 0) {
        this.sensePollFollowUpChoices = [];
        this.sensePollFollowUpChoices = [...this.sensePollFollowUpChoices];
        this.sensePollFollowUpQuestion = '';
      }
    } else if (e.detail.name === 'deleteSenseQueryCategory') {
      let buff = [];
      for (let i = 0; i < this.senseQueryCategories.length; i++) {
        if (this.senseQueryCategories[i].value !== e.detail.value) {
          buff.push(this.senseQueryCategories[i]);
        }
      }
      this.senseQueryCategories = buff;
      this.senseQueryCategories = [...this.senseQueryCategories];
    } else if (e.detail.name === 'deleteSensePriorityItem') {
      let buff = [];
      for (let i = 0; i < this.sensePriorityItems.length; i++) {
        if (this.sensePriorityItems[i].value !== e.detail.value) {
          buff.push(this.sensePriorityItems[i]);
        }
      }
      this.sensePriorityItems = buff;
      this.sensePriorityItems = [...this.sensePriorityItems];
    } else if (e.detail.name === 'deleteSenseChoiceAttribute') {
      let buff = [];
      for (let i = 0; i < this.senseChoiceAttributes.length; i++) {
        if (this.senseChoiceAttributes[i].value !== e.detail.value) {
          buff.push(this.senseChoiceAttributes[i]);
        }
      }
      this.senseChoiceAttributes = buff;
      this.senseChoiceAttributes = [...this.senseChoiceAttributes];
    } else if (e.detail.name.startsWith('deleteVariantFor-')) {
      const attributeValue = e.detail.name.replace('deleteVariantFor-', '');
      const currentVariants = this.senseChoiceAttributeVariants[attributeValue] || [];
      let buff = [];
      for (let i = 0; i < currentVariants.length; i++) {
        if (currentVariants[i].value !== e.detail.value) {
          buff.push(currentVariants[i]);
        }
      }
      this.senseChoiceAttributeVariants = {
        ...this.senseChoiceAttributeVariants,
        [attributeValue]: buff,
      };
    } else if (e.detail.name === 'deleteRespondentDetails') {
      let buff = [];
      for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
        if (this.surveyRespondentDetails[i].value !== e.detail.value) {
          buff.push(this.surveyRespondentDetails[i]);
        }
      }
      this.surveyRespondentDetails = buff;
      this.surveyRespondentDetails = [...this.surveyRespondentDetails];
    }
  }

  @Listen('inputEvent') handleInputEvent(e) {
    if (e.detail.name === 'surveyTitle') {
      this.surveyTitle = e.detail.value;
    } else if (e.detail.name === 'surveyDescription') {
      this.surveyDescription = e.detail.value;
    } else if (e.detail.name === 'surveyEmbedUrl') {
      this.surveyEmbedUrl = e.detail.value;
    } else if (e.detail.name === 'surveyDistributionMode') {
      this.surveyDistributionMode = e.detail.value;
      if (this.surveyDistributionMode === 'link') {
        this.surveyEmbedUrl = '';
      }
      this.handleCaptureWebpageUrl(this.surveyDistributionMode);
    } else if (e.detail.name === 'sensePollChoiceType') {
      this.sensePollChoiceType = e.detail.value;
    } else if (e.detail.name === 'sensePollQuestion') {
      this.sensePollQuestion = e.detail.value;
    } else if (e.detail.name === 'sensePollFollowUpChoice') {
      if (e.detail.isChecked) {
        let obj = {
          label: e.detail.label,
          value: e.detail.value,
        };
        this.sensePollFollowUpChoices.push(obj);
        this.sensePollFollowUpChoices = [...this.sensePollFollowUpChoices];
      } else {
        let buff = [];
        for (let i = 0; i < this.sensePollFollowUpChoices.length; i++) {
          if (this.sensePollFollowUpChoices[i].value !== e.detail.value) {
            buff.push(this.sensePollFollowUpChoices[i]);
          }
        }
        this.sensePollFollowUpChoices = buff;
        this.sensePollFollowUpChoices = [...this.sensePollFollowUpChoices];

        if (this.sensePollFollowUpChoices.length === 0) {
          this.sensePollFollowUpQuestion = '';
        }
      }
    } else if (e.detail.name === 'sensePollFollowUpQuestion') {
      this.sensePollFollowUpQuestion = e.detail.value;
    } else if (e.detail.name === 'senseQueryQuestion') {
      this.senseQueryQuestion = e.detail.value;
    } else if (e.detail.name === 'senseQueryCategory') {
      this.senseQueryCategory = e.detail.value;
    } else if (e.detail.name === 'sensePriorityQuestion') {
      this.sensePriorityQuestion = e.detail.value;
    } else if (e.detail.name === 'sensePriorityMaxPriorities') {
      this.sensePriorityMaxPriorities = parseInt(e.detail.value) || 3;
    } else if (e.detail.name === 'senseChoiceType') {
      this.senseChoiceType = e.detail.value;
    } else if (e.detail.name === 'sensePriceCurrency') {
      this.sensePriceCurrency = e.detail.value;
    } else if (e.detail.name === 'sensePriceCurrencyPreference') {
      this.sensePriceCurrencyPreference = e.detail.value;
    } else if (e.detail.name === 'sensePricePriceType') {
      this.sensePricePriceType = e.detail.value;
      // Reset recurring basis when price type changes
      if (e.detail.value !== 'recurring') {
        this.sensePriceRecurringBasis = '';
      }
    } else if (e.detail.name === 'sensePriceRecurringBasis') {
      this.sensePriceRecurringBasis = e.detail.value;
    } else if (e.detail.name === 'surveyThankYouMessage') {
      this.surveyThankYouMessage = e.detail.value;
    } else if (e.detail.name === 'productServiceType') {
      this.productServiceType = e.detail.value;
      // Reset dependent fields when type changes
      this.productType = '';
      this.serviceType = '';
      this.productIndustry = '';
      this.serviceIndustry = '';
      // Trigger dropdown resets
      this.productTypeResetTrigger = Date.now();
      this.serviceTypeResetTrigger = Date.now();
      this.productIndustryResetTrigger = Date.now();
      this.serviceIndustryResetTrigger = Date.now();
    } else if (e.detail.name === 'productType') {
      this.productType = e.detail.value;
    } else if (e.detail.name === 'serviceType') {
      this.serviceType = e.detail.value;
    } else if (e.detail.name === 'productIndustry') {
      this.productIndustry = e.detail.value;
    } else if (e.detail.name === 'serviceIndustry') {
      this.serviceIndustry = e.detail.value;
    } else if (e.detail.name === 'productServiceName') {
      this.productServiceName = e.detail.value;
    } else if (e.detail.name === 'productServiceDescription') {
      this.productServiceDescription = e.detail.value;
    }
  }

  @Listen('addCustomRespondentDetail') handleAddCustomRespondentDetail(e) {
    FrontendLogger.debug('Received addCustomRespondentDetail:', e.detail.respondentDetail);
    const newDetail = e.detail.respondentDetail;
    const isEditMode = this.isEditMode;

    if (isEditMode && this.editingIndex >= 0) {
      // Update existing detail
      this.surveyRespondentDetails[this.editingIndex] = newDetail;
      this.surveyRespondentDetails = [...this.surveyRespondentDetails];
    } else {
      // Check if the respondent detail already exists in the array
      let isDuplicate = false;
      for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
        if (this.surveyRespondentDetails[i].value === newDetail.value) {
          isDuplicate = true;
          break;
        }
      }

      // Only add if it's not a duplicate
      if (isDuplicate) {
        return;
      }

      // Add the new detail to the array
      this.surveyRespondentDetails = [...this.surveyRespondentDetails, newDetail];
    }

    // Close the modal and reset edit state
    this.isCustomDetailModalOpen = false;
    this.isEditMode = false;
    this.editingDetail = null;
    this.editingIndex = -1;

    // Reset the dropdown to "Choose Respondent Details"
    this.resetRespondentDetailsSelectTrigger = Date.now();
  }

  @Listen('modalCloseEvent') handleModalCloseEvent() {
    // Handle respondent detail modal close
    if (this.isCustomDetailModalOpen) {
      this.isCustomDetailModalOpen = false;
      this.isEditMode = false;
      this.editingDetail = null;
      this.editingIndex = -1;

      // Reset the dropdown to "Choose Respondent Details"
      this.resetRespondentDetailsSelectTrigger = Date.now();
    }

    // Handle priority item modal close
    if (this.isPriorityItemModalOpen) {
      this.isPriorityItemModalOpen = false;
      this.isEditingPriorityItem = false;
      this.editingPriorityItem = null;
      this.editingPriorityItemIndex = -1;
    }

    // Handle category modal close
    if (this.isCategoryModalOpen) {
      this.isCategoryModalOpen = false;
      this.senseQueryCategory = '';
    }

    // Handle category info modal close
    if (this.isCategoryInfoModalOpen) {
      this.isCategoryInfoModalOpen = false;
    }

    // Handle priority item info modal close
    if (this.isPriorityItemInfoModalOpen) {
      this.isPriorityItemInfoModalOpen = false;
    }

    // Handle poll choice modal close
    if (this.isPollChoiceModalOpen) {
      this.isPollChoiceModalOpen = false;
    }

    // Handle attribute modal close
    if (this.isAttributeModalOpen) {
      this.isAttributeModalOpen = false;
    }

    // Handle variant modal close
    if (this.isVariantModalOpen) {
      this.isVariantModalOpen = false;
      this.currentAttributeForVariants = '';
    }

    // Handle variant info modal close
    if (this.isVariantInfoModalOpen) {
      this.isVariantInfoModalOpen = false;
    }
  }

  @Listen('addSenseQueryCategoryFromModal')
  handleAddSenseQueryCategoryFromModal(e: CustomEvent) {
    const categoryValue = e.detail.category;
    if (!categoryValue || !categoryValue.trim()) {
      return;
    }

    // Check for duplicates
    let isDuplicate = false;
    for (let i = 0; i < this.senseQueryCategories.length; i++) {
      if (
        this.senseQueryCategories[i].value === categoryValue ||
        this.senseQueryCategories[i].label === categoryValue
      ) {
        isDuplicate = true;
        break;
      }
    }

    if (!isDuplicate) {
      let obj = {
        label: categoryValue,
        value: categoryValue.toLowerCase().replace(/\s+/g, '-'),
      };
      this.senseQueryCategories.push(obj);
      this.senseQueryCategories = [...this.senseQueryCategories];
    }

    // Close modal and reset
    this.isCategoryModalOpen = false;
    this.senseQueryCategory = '';
  }

  @Listen('addSensePollChoiceFromModal')
  handleAddSensePollChoiceFromModal(e: CustomEvent) {
    const choiceValue = e.detail.choice;
    if (!choiceValue || !choiceValue.trim()) {
      return;
    }

    const choiceObj = {
      label: choiceValue.trim(),
      value: choiceValue.trim().toLowerCase().replace(/\s+/g, '-'),
    };

    this.sensePollChoices.push(choiceObj);
    this.sensePollChoices = [...this.sensePollChoices];

    // Close modal
    this.isPollChoiceModalOpen = false;
  }

  @Listen('respondentDetailDeleteEvent') handleRespondentDetailDeleteEvent(e) {
    const valueToDelete = e.detail.value;

    // Filter out the detail to delete
    let updatedDetails = [];
    for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
      if (this.surveyRespondentDetails[i].value !== valueToDelete) {
        updatedDetails.push(this.surveyRespondentDetails[i]);
      }
    }

    this.surveyRespondentDetails = updatedDetails;
  }

  @Listen('addCustomPriorityItem')
  handleAddCustomPriorityItem(e: CustomEvent) {
    const newItem = e.detail.priorityItem;
    const isEditMode = this.isEditingPriorityItem;

    if (isEditMode && this.editingPriorityItemIndex >= 0) {
      // Update existing item
      this.sensePriorityItems[this.editingPriorityItemIndex] = newItem;
      this.sensePriorityItems = [...this.sensePriorityItems];
    } else {
      // Check if this item already exists (for new items)
      let alreadyExists = false;
      for (let i = 0; i < this.sensePriorityItems.length; i++) {
        if (this.sensePriorityItems[i].value === newItem.value) {
          alreadyExists = true;
          break;
        }
      }

      if (!alreadyExists) {
        this.sensePriorityItems = [...this.sensePriorityItems, newItem];
      }
    }

    // Close the modal and reset edit state
    this.isPriorityItemModalOpen = false;
    this.isEditingPriorityItem = false;
    this.editingPriorityItem = null;
    this.editingPriorityItemIndex = -1;
  }

  @Listen('priorityItemDeleteEvent')
  handlePriorityItemDeleteEvent(e: CustomEvent) {
    const valueToDelete = e.detail.value;

    // Filter out the item to delete
    let updatedItems = [];
    for (let i = 0; i < this.sensePriorityItems.length; i++) {
      if (this.sensePriorityItems[i].value !== valueToDelete) {
        updatedItems.push(this.sensePriorityItems[i]);
      }
    }

    this.sensePriorityItems = updatedItems;
  }

  @Listen('priorityItemEditEvent')
  handlePriorityItemEditEvent(e: CustomEvent) {
    this.editingPriorityItem = e.detail.item;
    this.editingPriorityItemIndex = e.detail.index;
    this.isEditingPriorityItem = true;
    this.isPriorityItemModalOpen = true;
  }

  @Listen('selectChangeEvent') handleSelectChangeEvent(e) {
    if (e.detail.name === 'productType') {
      this.productType = e.detail.value;
    } else if (e.detail.name === 'serviceType') {
      this.serviceType = e.detail.value;
    } else if (e.detail.name === 'productIndustry') {
      this.productIndustry = e.detail.value;
    } else if (e.detail.name === 'serviceIndustry') {
      this.serviceIndustry = e.detail.value;
    } else if (e.detail.name === 'sensePriceCurrency') {
      this.sensePriceCurrency = e.detail.value;
    } else if (e.detail.name === 'surveyRespondentDetails') {
      if (e.detail.value === '-') {
        return;
      }

      // If "Create Custom Detail" is selected, open the modal
      if (e.detail.value === 'custom') {
        this.isEditMode = false;
        this.editingDetail = null;
        this.editingIndex = -1;
        this.isCustomDetailModalOpen = true;
        return;
      }

      // Find the selected option in RespondentDetailsOptions to get inputType
      const selectedOption = RespondentDetailsOptions.find(
        option => option.value === e.detail.value,
      );

      let obj = {
        label: e.detail.label,
        value: e.detail.value,
        inputType: selectedOption?.inputType || 'text',
        placeholder: selectedOption?.placeholder,
        options: selectedOption?.options,
        required: selectedOption?.required || false,
        defaultValue: selectedOption?.defaultValue,
      };

      // Check if the respondent detail already exists in the array
      let isDuplicate = false;
      for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
        if (this.surveyRespondentDetails[i].value === obj.value) {
          isDuplicate = true;
          break;
        }
      }

      // Only add if it's not a duplicate
      if (isDuplicate) {
        return;
      }

      this.surveyRespondentDetails.push(obj);
      this.surveyRespondentDetails = [...this.surveyRespondentDetails];

      // Reset the dropdown after adding an item
      this.resetRespondentDetailsSelectTrigger = Date.now();
    }
  }

  private handleCaptureWebpageUrl(value: string) {
    if (value === 'embed' || value === 'embedlink') {
      this.isEmbedMode = true;
    } else {
      this.isEmbedMode = false;
    }
  }

  private handleSurveyTypeSelection(value: string) {
    this.surveyType = value;
    this.resetWizard();
  }

  private handleSenseChoiceTypeSelection(value: string) {
    this.senseChoiceType = value;
  }

  @Listen('addCustomSenseChoiceAttribute')
  handleAddCustomSenseChoiceAttribute(e: CustomEvent) {
    const newAttribute = e.detail.attribute;

    const maxAttributes = this.senseChoiceType === 'lite' ? 4 : 7;
    if (this.senseChoiceAttributes.length >= maxAttributes) {
      return;
    }

    // Check if this attribute already exists
    let alreadyExists = false;
    for (let i = 0; i < this.senseChoiceAttributes.length; i++) {
      if (this.senseChoiceAttributes[i].value === newAttribute.value) {
        alreadyExists = true;
        break;
      }
    }

    if (!alreadyExists) {
      this.senseChoiceAttributes = [...this.senseChoiceAttributes, newAttribute];
    }

    // Close the modal
    this.isAttributeModalOpen = false;
  }

  @Listen('addCustomSenseChoiceVariant')
  handleAddCustomSenseChoiceVariant(e: CustomEvent) {
    const newVariant = e.detail.variant;
    const attributeValue = this.currentAttributeForVariants;

    if (!attributeValue) return;

    const currentVariants = this.senseChoiceAttributeVariants[attributeValue] || [];

    // Create variant object with label and value
    const variantObj = {
      label: newVariant,
      value: newVariant
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, ''),
    };

    // Check if this variant already exists
    let alreadyExists = false;
    for (let i = 0; i < currentVariants.length; i++) {
      if (currentVariants[i].value === variantObj.value) {
        alreadyExists = true;
        break;
      }
    }

    if (!alreadyExists) {
      this.senseChoiceAttributeVariants = {
        ...this.senseChoiceAttributeVariants,
        [attributeValue]: [...currentVariants, variantObj],
      };
    }

    // Close the modal
    this.isVariantModalOpen = false;
    this.currentAttributeForVariants = '';
  }

  private generateConceptsFromAttributesAndVariants() {
    if (this.senseChoiceAttributes.length === 0) return;

    // Generate all possible combinations
    const attributeValues = this.senseChoiceAttributes.map(attribute => attribute.value);
    const variantCombinations = this.cartesianProduct(
      attributeValues.map(attributeValue =>
        (this.senseChoiceAttributeVariants[attributeValue] || []).map(variant => variant.value),
      ),
    );

    // Create concept objects
    this.senseChoiceGeneratedConcepts = variantCombinations.map((combination, index) => {
      const conceptAttributes: { [key: string]: string } = {};

      attributeValues.forEach((attributeValue, attributeIndex) => {
        conceptAttributes[attributeValue] = combination[attributeIndex];
      });

      return {
        conceptId: `concept_${String(index + 1).padStart(3, '0')}`,
        attributes: conceptAttributes,
        selected: false, // Default all to unselected - user must choose
      };
    });

    // Initialize selected concepts (empty since none are selected by default)
    this.senseChoiceSelectedConcepts = [];
  }

  private generateTasksFromSelectedConcepts() {
    if (this.senseChoiceSelectedConcepts.length < 2) return;

    const selectedConcepts = this.senseChoiceSelectedConcepts.filter(c => c.selected);
    const numTasks = this.senseChoiceType === 'lite' ? 6 : 12;

    this.senseChoiceGeneratedTasks = [];

    for (let i = 0; i < Math.min(numTasks, Math.floor(selectedConcepts.length / 2)); i++) {
      // Simple pairing for now - can be enhanced with better experimental design
      const concept1 = selectedConcepts[i * 2];
      const concept2 = selectedConcepts[i * 2 + 1] || selectedConcepts[0];

      this.senseChoiceGeneratedTasks.push({
        taskId: `task_${String(i + 1).padStart(3, '0')}`,
        taskNumber: i + 1,
        alternatives: [concept1, concept2],
        includeNoneOption: true,
        selected: false, // Default to unselected - user must choose
      });
    }

    this.senseChoiceSelectedTasks = []; // Empty since none are selected by default
  }

  private cartesianProduct(arrays: string[][]): string[][] {
    return arrays.reduce((acc, curr) => acc.flatMap(x => curr.map(y => [...x, y])), [
      [],
    ] as string[][]);
  }

  private toggleConcept(conceptId: string) {
    this.senseChoiceGeneratedConcepts = this.senseChoiceGeneratedConcepts.map(concept =>
      concept.conceptId === conceptId ? { ...concept, selected: !concept.selected } : concept,
    );
    // Update selected concepts array
    this.senseChoiceSelectedConcepts = this.senseChoiceGeneratedConcepts.filter(c => c.selected);
  }

  private getSelectedConceptsCount(): number {
    return this.senseChoiceGeneratedConcepts.filter(c => c.selected).length;
  }

  private getConceptTypeName(): string {
    return this.productServiceType === 'product' ? 'Product Concept' : 'Service Concept';
  }

  private toggleTask(taskId: string) {
    this.senseChoiceGeneratedTasks = this.senseChoiceGeneratedTasks.map(task =>
      task.taskId === taskId ? { ...task, selected: !task.selected } : task,
    );
    // Update selected tasks array
    this.senseChoiceSelectedTasks = this.senseChoiceGeneratedTasks.filter(t => t.selected);
  }

  private getSelectedTasksCount(): number {
    return this.senseChoiceGeneratedTasks.filter(t => t.selected).length;
  }

  private generateSurveyName(value: string) {
    if (value === 'sensePrice') {
      return 'SensePrice';
    } else if (value === 'senseChoice') {
      return 'SenseChoice';
    } else if (value === 'sensePoll') {
      return 'SensePoll';
    } else if (value === 'senseQuery') {
      return 'SenseQuery';
    } else if (value === 'sensePriority') {
      return 'SensePriority';
    }
  }

  private getDisplayStepNumber() {
    // For SenseQuery, adjust step numbers since we skip step 3
    if (this.surveyType === 'senseQuery' && this.currentStepIndex >= 4) {
      return this.currentStepIndex; // Step 4 becomes step 4 in display (since we skip step 3)
    }
    // For SenseChoice, adjust step numbers to account for sub-steps
    if (this.surveyType === 'senseChoice' && this.currentStepIndex === 3) {
      return this.currentStepIndex + this.senseChoiceConfigStep; // 3 + 1 = 4, 3 + 2 = 5, etc.
    }
    // For SenseChoice, adjust subsequent steps
    if (this.surveyType === 'senseChoice' && this.currentStepIndex > 3) {
      return this.currentStepIndex + 3; // Add 3 for the 4 sub-steps (1,2,3,4) minus the 1 original step
    }
    return this.currentStepIndex + 1;
  }

  private getDisplayTotalSteps() {
    // For SenseQuery, we have one fewer step since we skip Configuration (2)
    if (this.surveyType === 'senseQuery') {
      return this.surveySteps.length - 1;
    }
    // For SenseChoice, we have 3 additional sub-steps in Configuration (2)
    if (this.surveyType === 'senseChoice') {
      return this.surveySteps.length + 3; // Add 3 for the 4 sub-steps minus the 1 original step
    }
    return this.surveySteps.length;
  }

  async createSurvey() {
    if (this.surveyType === 'sensePoll') {
      this.surveyConfig = generateSensePollConfig(
        this.sensePollQuestion,
        this.sensePollChoiceType,
        this.sensePollChoices,
        this.sensePollFollowUpChoices,
        this.sensePollFollowUpQuestion,
        this.surveyThankYouMessage,
      );
    } else if (this.surveyType === 'senseQuery') {
      this.surveyConfig = generateSenseQueryConfig(
        this.senseQueryQuestion,
        this.senseQueryCategories,
        this.surveyThankYouMessage,
      );
    } else if (this.surveyType === 'sensePriority') {
      this.surveyConfig = generateSensePriorityConfig(
        this.sensePriorityQuestion,
        this.sensePriorityItems,
        this.surveyThankYouMessage,
      );
    } else if (this.surveyType === 'senseChoice') {
      this.surveyConfig = generateSenseChoiceConfig(
        this.senseChoiceType,
        this.senseChoiceAttributes,
        this.senseChoiceAttributeVariants,
        this.senseChoiceSelectedConcepts.filter(c => c.selected),
        this.senseChoiceSelectedTasks.filter(t => t.selected),
        this.surveyThankYouMessage,
      );
    } else if (this.surveyType === 'sensePrice') {
      this.surveyConfig = generateSensePriceConfig(
        this.sensePriceCurrency,
        this.sensePricePriceType,
        this.sensePriceRecurringBasis,
        this.surveyThankYouMessage,
      );
    }

    let createSurveyPayload: createSurveyPayloadInterface = generateCreateSurveyPayload(
      this.surveyType,
      this.surveyTitle,
      this.surveyDistributionMode,
      this.surveyEmbedUrl,
      this.surveyTags,
      this.surveyConfig,
      this.surveyRespondentDetails,
    );

    // Debug logging to see what's being validated
    FrontendLogger.debug('Survey payload before validation:', createSurveyPayload);
    FrontendLogger.debug('Respondent details:', this.surveyRespondentDetails);

    let { isValid, validationMessage } = validateCreateSurveyPayload(createSurveyPayload);

    if (!isValid) {
      return alert(validationMessage);
    }

    this.isCreatingSurvey = true;
    let { success, message } = await createSurveyApi(createSurveyPayload);
    this.isCreatingSurvey = false;
    if (!success) {
      return alert(message);
    }
    Router.push('/');
  }

  private resetWizard() {
    /* Survey Basics States */
    this.surveyTitle = '';
    this.surveyDescription = '';
    this.surveyDistributionMode = '';
    this.surveyEmbedUrl = '';
    this.isEmbedMode = false;
    this.surveyTags = [];
    this.surveyTags = [...this.surveyTags];
    this.surveyConfig = {};
    this.surveyRespondentDetails = [];
    this.surveyRespondentDetails = [...this.surveyRespondentDetails];
    this.surveyThankYouMessage = 'Thank you for your response!';

    /* SensePoll States */
    this.sensePollQuestion = '';
    this.sensePollChoiceType = '';
    this.sensePollChoices = [];
    this.sensePollChoices = [...this.sensePollChoices];
    this.sensePollFollowUpChoices = [];
    this.sensePollFollowUpChoices = [...this.sensePollFollowUpChoices];
    this.isPollChoiceModalOpen = false;

    /* SenseQuery States */
    this.senseQueryQuestion = '';
    this.senseQueryCategories = [];
    this.senseQueryCategories = [...this.senseQueryCategories];
    this.senseQueryCategory = '';
    this.isCategoryModalOpen = false;

    /* SensePriority States */
    this.sensePriorityQuestion = '';
    this.sensePriorityItems = [];
    this.sensePriorityItems = [...this.sensePriorityItems];
    this.sensePriorityMaxPriorities = 3;
    this.isPriorityItemModalOpen = false;
    this.isEditingPriorityItem = false;
    this.editingPriorityItem = null;
    this.editingPriorityItemIndex = -1;

    /* SenseChoice States */
    this.senseChoiceType = '';
    this.senseChoiceAttributes = [];
    this.senseChoiceAttributes = [...this.senseChoiceAttributes];
    this.senseChoiceAttributeVariants = {};
    this.senseChoiceConfigStep = 1;
    this.senseChoiceGeneratedConcepts = [];
    this.senseChoiceSelectedConcepts = [];
    this.senseChoiceGeneratedTasks = [];
    this.senseChoiceSelectedTasks = [];
    this.isAttributeModalOpen = false;
    this.isVariantModalOpen = false;
    this.isVariantInfoModalOpen = false;
    this.currentAttributeForVariants = '';

    /* Product/Service Configuration States */
    this.productServiceType = '';
    this.productType = '';
    this.serviceType = '';
    this.productIndustry = '';
    this.serviceIndustry = '';
    this.productServiceName = '';
    this.productServiceDescription = '';
    this.productTypeResetTrigger = 0;
    this.serviceTypeResetTrigger = 0;
    this.productIndustryResetTrigger = 0;
    this.serviceIndustryResetTrigger = 0;

    /* Survey Respondent States */
    this.surveyRespondentDetails = [];
    this.surveyRespondentDetails = [...this.surveyRespondentDetails];
  }

  private basicStepNextButton(title: string, distribution: string, embedUrl: string) {
    let isTitleReady: boolean = false;
    let isDistributionReady: boolean = false;

    if (title.length > 0) {
      isTitleReady = true;
    } else {
      isTitleReady = false;
    }

    if (distribution === 'embed' || distribution === 'embedlink') {
      if (embedUrl.length === 0) {
        isDistributionReady = false;
      } else {
        let { isValid } = ValidateUrlPayload({ url: embedUrl });
        if (!isValid) {
          isDistributionReady = false;
        } else {
          isDistributionReady = true;
        }
      }
    } else if (distribution === 'link') {
      isDistributionReady = true;
    } else {
      isDistributionReady = false;
    }

    return !isTitleReady || !isDistributionReady;
  }

  private checkExistenceInFollowUpChoices(value: string) {
    let exists: boolean = false;

    if (this.sensePollFollowUpChoices.length === 0) {
      return false;
    }

    for (let i = 0; i < this.sensePollFollowUpChoices.length; i++) {
      if (this.sensePollFollowUpChoices[i] && this.sensePollFollowUpChoices[i].value === value) {
        exists = true;
        break;
      }
    }
    return exists;
  }

  private pollSenseConfig2NextButton(choices: any, followUpChoices: any, followUpQuestion: string) {
    let isChoicesReady: boolean = false;
    let isFollowUpReady: boolean = false;

    if (choices.length > 0) {
      isChoicesReady = true;
    } else {
      isChoicesReady = false;
    }

    if (followUpChoices.length > 0) {
      if (followUpQuestion.length > 0) {
        isFollowUpReady = true;
      } else {
        isFollowUpReady = false;
      }
    } else {
      isFollowUpReady = true;
    }

    return !isChoicesReady || !isFollowUpReady;
  }

  /* Local Components */
  SurveyRadio: FunctionalComponent<SurveyRadioProps> = ({ label, description, value, color }) => (
    <div
      class={`survey-radio ${this.surveyType === value && 'survey-radio--active'}`}
      data-color={color}
      onClick={() => this.handleSurveyTypeSelection(value)}
    >
      <e-text class={`survey-title survey-title--${color}`}>
        <strong>{label}</strong>
      </e-text>
      <l-spacer value={0.25}></l-spacer>
      <e-text variant="footnote">{description}</e-text>
    </div>
  );

  SenseChoiceRadio: FunctionalComponent<SurveyRadioProps> = ({
    label,
    description,
    value,
    color,
  }) => (
    <div
      class={`survey-radio ${this.senseChoiceType === value && 'survey-radio--active'}`}
      data-color={color}
      onClick={() => this.handleSenseChoiceTypeSelection(value)}
    >
      <e-text class={`survey-title survey-title--${color}`}>
        <strong>{label}</strong>
      </e-text>
      <l-spacer value={0.25}></l-spacer>
      <e-text variant="footnote">{description}</e-text>
    </div>
  );

  /* SenseQuery Wizard Steps */
  SenseQueryConfig1: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Survey Prompt <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="text"
        name="senseQueryQuestion"
        placeholder="e.g. Do you have any queries about installation?"
        value={this.senseQueryQuestion}
      ></e-input>
      <l-spacer value={2.5}></l-spacer>
      <l-row justifyContent="space-between">
        <l-row justifyContent="flex-start">
          <e-text>
            <strong>Question categories</strong>
          </e-text>
          <l-spacer variant="horizontal" value={0.25}></l-spacer>
          <e-button variant="link" action="showCategoryInfo">
            <e-image src="../../../assets/icon/light/info-light.svg" width="1.2em"></e-image>
          </e-button>
        </l-row>
        <e-button variant="link" action="addSenseQueryCategory">
          + Add Category
        </e-button>
      </l-row>
      <l-spacer value={0.5}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={1}></l-spacer>
      <p-list-with-delete
        name="deleteSenseQueryCategory"
        items={JSON.stringify(this.senseQueryCategories)}
        emptyMessage="No categories added yet"
      ></p-list-with-delete>
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button disabled={this.senseQueryQuestion ? false : true} action="nextStep">
          Next
        </e-button>
      </div>
    </div>
  );

  /* SensePriority Wizard Steps */
  SensePriorityConfig1: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Is this survey about a product or service? <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <l-row justifyContent="flex-start">
        <e-input
          type="radio"
          name="productServiceType"
          value="product"
          checked={this.productServiceType === 'product' ? true : false}
        >
          Product
        </e-input>
        <l-spacer variant="horizontal" value={1}></l-spacer>
        <e-input
          type="radio"
          name="productServiceType"
          value="service"
          checked={this.productServiceType === 'service' ? true : false}
        >
          Service
        </e-input>
      </l-row>
      <l-spacer value={2.5}></l-spacer>

      {this.productServiceType === 'product' && (
        <div>
          <e-text>
            <strong>
              Product type <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="productType"
            options={JSON.stringify(ProductTypes)}
            resetTrigger={this.productTypeResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>
              Which industry is this product for? <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="productIndustry"
            options={JSON.stringify(ProductIndustries)}
            resetTrigger={this.productIndustryResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Product name</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceName"
            placeholder="e.g. Task Management App"
            value={this.productServiceName}
          ></e-input>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Product description</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceDescription"
            placeholder="e.g. Collaborative task management platform for teams"
            value={this.productServiceDescription}
          ></e-input>
        </div>
      )}

      {this.productServiceType === 'service' && (
        <div>
          <e-text>
            <strong>
              Service type <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="serviceType"
            options={JSON.stringify(ServiceTypes)}
            resetTrigger={this.serviceTypeResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>
              Which industry is this service for? <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="serviceIndustry"
            options={JSON.stringify(ServiceIndustries)}
            resetTrigger={this.serviceIndustryResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Service name</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceName"
            placeholder="e.g. Project Management Consulting"
            value={this.productServiceName}
          ></e-input>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Service description</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceDescription"
            placeholder="e.g. Expert project management consulting and implementation"
            value={this.productServiceDescription}
          ></e-input>
        </div>
      )}

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={
            !this.productServiceType ||
            (this.productServiceType === 'product' &&
              (!this.productType || !this.productIndustry)) ||
            (this.productServiceType === 'service' && (!this.serviceType || !this.serviceIndustry))
              ? true
              : false
          }
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  /* SensePrice Wizard Steps */
  SensePriceConfig1: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Is this survey about a product or service? <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <l-row justifyContent="flex-start">
        <e-input
          type="radio"
          name="productServiceType"
          value="product"
          checked={this.productServiceType === 'product' ? true : false}
        >
          Product
        </e-input>
        <l-spacer variant="horizontal" value={1}></l-spacer>
        <e-input
          type="radio"
          name="productServiceType"
          value="service"
          checked={this.productServiceType === 'service' ? true : false}
        >
          Service
        </e-input>
      </l-row>
      <l-spacer value={2.5}></l-spacer>

      {this.productServiceType === 'product' && (
        <div>
          <e-text>
            <strong>
              Product type <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="productType"
            options={JSON.stringify(ProductTypes)}
            resetTrigger={this.productTypeResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>
              Which industry is this product for? <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="productIndustry"
            options={JSON.stringify(ProductIndustries)}
            resetTrigger={this.productIndustryResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Product name</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceName"
            placeholder="e.g. iPhone 15 Pro"
            value={this.productServiceName}
          ></e-input>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Product description</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceDescription"
            placeholder="e.g. Latest flagship smartphone with advanced camera system"
            value={this.productServiceDescription}
          ></e-input>
        </div>
      )}

      {this.productServiceType === 'service' && (
        <div>
          <e-text>
            <strong>
              Service type <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="serviceType"
            options={JSON.stringify(ServiceTypes)}
            resetTrigger={this.serviceTypeResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>
              Which industry is this service for? <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="serviceIndustry"
            options={JSON.stringify(ServiceIndustries)}
            resetTrigger={this.serviceIndustryResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Service name</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceName"
            placeholder="e.g. Premium Consulting Package"
            value={this.productServiceName}
          ></e-input>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Service description</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceDescription"
            placeholder="e.g. Comprehensive business strategy consulting for startups"
            value={this.productServiceDescription}
          ></e-input>
        </div>
      )}

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={
            !this.productServiceType ||
            (this.productServiceType === 'product' &&
              (!this.productType || !this.productIndustry)) ||
            (this.productServiceType === 'service' && (!this.serviceType || !this.serviceIndustry))
              ? true
              : false
          }
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  SensePriceConfig2: FunctionalComponent = () => (
    <div>
      <div class="notice">
        <e-text variant="footnote">
          <strong>💡 How this works:</strong> These settings will determine how pricing options are
          presented to respondents in your SensePrice survey. The currency and price type you select
          will be used to display price ranges and collect pricing preferences from your audience.
        </e-text>
      </div>
      <l-spacer value={2}></l-spacer>

      <e-text>
        <strong>
          Which currency should respondents use? <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <div>
        <e-input
          type="radio"
          name="sensePriceCurrencyPreference"
          value="local"
          checked={this.sensePriceCurrencyPreference === 'local' ? true : false}
        >
          Local currency (respondents answer in their own currency)
        </e-input>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="radio"
          name="sensePriceCurrencyPreference"
          value="fixed"
          checked={this.sensePriceCurrencyPreference === 'fixed' ? true : false}
        >
          Fixed currency (all respondents see the same currency)
        </e-input>
      </div>
      <l-spacer value={2.5}></l-spacer>

      <e-text>
        <strong>
          Currency Preference <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-select name="sensePriceCurrency" options={JSON.stringify(CurrencyOptions)}></e-select>
      <l-spacer value={0.25}></l-spacer>
      <e-text variant="footnote">
        This is the currency where survey results will be shown. If you selected "Fixed currency"
        above, this currency will also be displayed to respondents in the survey.
      </e-text>
      <l-spacer value={3.5}></l-spacer>

      <e-text>
        <strong>
          Payment schedule <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <l-row justifyContent="flex-start">
        <e-input
          type="radio"
          name="sensePricePriceType"
          value="non-recurring"
          checked={this.sensePricePriceType === 'non-recurring' ? true : false}
        >
          One-time payment
        </e-input>
        <l-spacer variant="horizontal" value={1}></l-spacer>
        <e-input
          type="radio"
          name="sensePricePriceType"
          value="recurring"
          checked={this.sensePricePriceType === 'recurring' ? true : false}
        >
          Subscription
        </e-input>
      </l-row>
      <l-spacer value={2.5}></l-spacer>

      {this.sensePricePriceType === 'recurring' && (
        <div>
          <e-text>
            <strong>
              Billing cycle <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <l-row justifyContent="flex-start">
            <e-input
              type="radio"
              name="sensePriceRecurringBasis"
              value="monthly"
              checked={this.sensePriceRecurringBasis === 'monthly' ? true : false}
            >
              Monthly
            </e-input>
            <l-spacer variant="horizontal" value={1}></l-spacer>
            <e-input
              type="radio"
              name="sensePriceRecurringBasis"
              value="annual"
              checked={this.sensePriceRecurringBasis === 'annual' ? true : false}
            >
              Annual
            </e-input>
          </l-row>
          <l-spacer value={2.5}></l-spacer>
        </div>
      )}

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={
            !this.sensePriceCurrency ||
            !this.sensePricePriceType ||
            (this.sensePricePriceType === 'recurring' && !this.sensePriceRecurringBasis)
              ? true
              : false
          }
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  /* SenseChoice Wizard Steps */
  SenseChoiceConfig1: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Is this survey about a product or service? <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <l-row justifyContent="flex-start">
        <e-input
          type="radio"
          name="productServiceType"
          value="product"
          checked={this.productServiceType === 'product' ? true : false}
        >
          Product
        </e-input>
        <l-spacer variant="horizontal" value={1}></l-spacer>
        <e-input
          type="radio"
          name="productServiceType"
          value="service"
          checked={this.productServiceType === 'service' ? true : false}
        >
          Service
        </e-input>
      </l-row>
      <l-spacer value={2.5}></l-spacer>

      {this.productServiceType === 'product' && (
        <div>
          <e-text>
            <strong>
              Product type <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="productType"
            options={JSON.stringify(ProductTypes)}
            resetTrigger={this.productTypeResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>
              Which industry is this product for? <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="productIndustry"
            options={JSON.stringify(ProductIndustries)}
            resetTrigger={this.productIndustryResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Product name</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceName"
            placeholder="e.g. Smart Home Security System"
            value={this.productServiceName}
          ></e-input>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Product description</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceDescription"
            placeholder="e.g. AI-powered security system with mobile app control"
            value={this.productServiceDescription}
          ></e-input>
        </div>
      )}

      {this.productServiceType === 'service' && (
        <div>
          <e-text>
            <strong>
              Service type <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="serviceType"
            options={JSON.stringify(ServiceTypes)}
            resetTrigger={this.serviceTypeResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>
              Which industry is this service for? <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-select
            name="serviceIndustry"
            options={JSON.stringify(ServiceIndustries)}
            resetTrigger={this.serviceIndustryResetTrigger}
          ></e-select>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Service name</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceName"
            placeholder="e.g. Digital Marketing Strategy"
            value={this.productServiceName}
          ></e-input>
          <l-spacer value={2.5}></l-spacer>

          <e-text>
            <strong>Service description</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="productServiceDescription"
            placeholder="e.g. Comprehensive digital marketing strategy and implementation"
            value={this.productServiceDescription}
          ></e-input>
        </div>
      )}

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={
            !this.productServiceType ||
            (this.productServiceType === 'product' &&
              (!this.productType || !this.productIndustry)) ||
            (this.productServiceType === 'service' && (!this.serviceType || !this.serviceIndustry))
              ? true
              : false
          }
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  /* SenseChoice Config 2 - Survey Type and Attributes */
  SenseChoiceConfig2: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          How detailed do you want your survey to be? <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={2.5}></l-spacer>
      <this.SenseChoiceRadio
        label="SenseChoice Lite"
        description="For quick tests. Takes 1-2 minutes to complete survey"
        value="lite"
        color="blue"
      ></this.SenseChoiceRadio>
      <this.SenseChoiceRadio
        label="SenseChoice Full"
        description="For deep insight. Takes 5-10 to complete survey"
        value="full"
        color="blue"
      ></this.SenseChoiceRadio>
      <l-spacer value={2.5}></l-spacer>

      {this.senseChoiceType && (
        <div>
          <div class="adjustment-row">
            <e-text>
              <strong>
                Attributes <span class="mandatory"> * </span>
              </strong>
              <span> (Max. {this.senseChoiceType === 'lite' ? '4' : '7'} attributes)</span>
            </e-text>
            <e-button
              variant="link"
              action="addSenseChoiceAttribute"
              disabled={
                this.senseChoiceAttributes.length >= (this.senseChoiceType === 'lite' ? 4 : 7)
              }
            >
              + Add Attribute
            </e-button>
          </div>
          <l-spacer value={1}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={1}></l-spacer>
          <p-list-with-delete
            name="deleteSenseChoiceAttribute"
            items={JSON.stringify(this.senseChoiceAttributes)}
            emptyMessage="No attributes added yet"
          ></p-list-with-delete>
        </div>
      )}

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={!this.senseChoiceType || this.senseChoiceAttributes.length === 0}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  /* SenseChoice Config 3 - Attribute Variants */
  SenseChoiceConfig3: FunctionalComponent = () => (
    <div>
      <l-row justifyContent="flex-start">
        <e-text>
          <strong>
            Add variants for each attribute <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <e-button
          variant="link"
          action="openVariantInfo"
          class="info-button"
          style={{ display: 'inline', marginLeft: '0.5em', verticalAlign: 'middle' }}
        >
          <e-image src="../../../assets/icon/light/info-light.svg" width="1.2em"></e-image>
        </e-button>
      </l-row>
      <l-spacer value={2}></l-spacer>

      {this.senseChoiceAttributes.length > 0 && (
        <div>
          {this.senseChoiceAttributes.map((attribute, index) => (
            <div>
              {index > 0 && <l-spacer value={2}></l-spacer>}
              <c-card>
                <div class="adjustment-row">
                  <div>
                    <e-text>
                      <strong>{attribute.label}</strong>
                      <span> (Min. 2 required)</span>
                    </e-text>
                  </div>
                  <e-button variant="link" action={`addVariantFor-${attribute.value}`}>
                    + Add Variant
                  </e-button>
                </div>
                <l-spacer value={1}></l-spacer>
                <l-separator></l-separator>
                <l-spacer value={1}></l-spacer>
                <p-list-with-delete
                  name={`deleteVariantFor-${attribute.value}`}
                  items={JSON.stringify(this.senseChoiceAttributeVariants[attribute.value] || [])}
                  emptyMessage="No variants added yet"
                ></p-list-with-delete>
              </c-card>
            </div>
          ))}
        </div>
      )}

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.senseChoiceAttributes.some(
            attribute =>
              !this.senseChoiceAttributeVariants[attribute.value] ||
              this.senseChoiceAttributeVariants[attribute.value].length < 2,
          )}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  /* SenseChoice Config 4 - Concept Selection */
  SenseChoiceConfig4: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Choose {this.getConceptTypeName().toLowerCase()}s for your study{' '}
          <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-text variant="footnote">
        Review all possible combinations and select only realistic options that you want to test.
        Uncheck any combinations that don't make sense for your{' '}
        {this.productServiceType || 'offering'}.
      </e-text>

      {this.senseChoiceGeneratedConcepts.length > 0 && (
        <div class="concepts-grid">
          {this.senseChoiceGeneratedConcepts.map((concept, index) => (
            <div
              class={`survey-radio ${concept.selected ? 'survey-radio--active' : ''}`}
              data-color="blue"
              onClick={() => this.toggleConcept(concept.conceptId)}
            >
              <e-text variant="footnote">
                {this.getConceptTypeName().toUpperCase()} {index + 1}
              </e-text>
              <l-spacer value={0.5}></l-spacer>
              {Object.entries(concept.attributes).map(([attributeValue, variantValue]) => {
                const attribute = this.senseChoiceAttributes.find(a => a.value === attributeValue);
                const variant = this.senseChoiceAttributeVariants[attributeValue]?.find(
                  v => v.value === variantValue,
                );
                return (
                  <div class="concept-attribute">
                    <e-text>
                      <strong>{attribute?.label}:</strong> {variant?.label}
                    </e-text>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      )}

      <l-spacer value={2}></l-spacer>
      <div class="selection-summary">
        <e-text variant="footnote">
          {this.getSelectedConceptsCount()} of {this.senseChoiceGeneratedConcepts.length}{' '}
          {this.getConceptTypeName().toLowerCase()}s selected (minimum{' '}
          {this.senseChoiceType === 'lite' ? '6' : '12'} required)
        </e-text>
      </div>

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.getSelectedConceptsCount() < (this.senseChoiceType === 'lite' ? 6 : 12)}
          action="nextStep"
        >
          Generate Choice Tasks
        </e-button>
      </div>
    </div>
  );

  /* SenseChoice Config 5 - Task Selection */
  SenseChoiceConfig5: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Choose choice tasks <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-text variant="footnote">
        We've generated {this.senseChoiceGeneratedTasks.length} choice tasks from your selected
        {this.getConceptTypeName().toLowerCase()}s. Review each task and customize as needed. Each
        task should present meaningful trade-offs.
      </e-text>
      <l-spacer value={2}></l-spacer>

      {this.senseChoiceGeneratedTasks.length > 0 && (
        <div class="choice-tasks-list">
          {this.senseChoiceGeneratedTasks.map(task => (
            <div
              class={`survey-radio ${task.selected ? 'survey-radio--active' : ''}`}
              data-color="blue"
              onClick={() => this.toggleTask(task.taskId)}
            >
              <e-text variant="footnote">CHOICE TASK {task.taskNumber}</e-text>

              <l-spacer value={1}></l-spacer>
              <e-text>
                <strong>Question:</strong> Which option would you prefer?
              </e-text>
              <l-spacer value={1.5}></l-spacer>
              <l-separator></l-separator>
              <l-spacer value={1.5}></l-spacer>
              <div class="alternatives-grid">
                {task.alternatives.map((concept, altIndex) => (
                  <div>
                    <div class="alternative-option">
                      <e-text variant="footnote">
                        OPTION {String.fromCharCode(65 + altIndex)}
                      </e-text>
                      <l-spacer value={0.25}></l-spacer>
                      {Object.entries(concept.attributes).map(([attributeValue, variantValue]) => {
                        const attribute = this.senseChoiceAttributes.find(
                          a => a.value === attributeValue,
                        );
                        const variant = this.senseChoiceAttributeVariants[attributeValue]?.find(
                          v => v.value === variantValue,
                        );
                        return (
                          <div class="attribute-line">
                            <e-text>
                              <strong>{attribute?.label}:</strong> {variant?.label}
                            </e-text>
                          </div>
                        );
                      })}
                    </div>
                    {/* {altIndex < task.alternatives.length - 1 && <l-spacer value={1}></l-spacer>} */}
                    <l-spacer value={1.5}></l-spacer>
                    <l-separator></l-separator>
                    <l-spacer value={1.5}></l-spacer>
                  </div>
                ))}

                {task.includeNoneOption && (
                  <div>
                    <l-spacer value={1}></l-spacer>
                    <div class="alternative-option none-option">
                      <e-text variant="footnote">
                        OPTION {String.fromCharCode(65 + task.alternatives.length)}
                      </e-text>
                      <l-spacer value={0.25}></l-spacer>
                      <e-text>None of these options</e-text>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <l-spacer value={2}></l-spacer>
      <div class="task-summary">
        <e-text variant="footnote">
          {this.getSelectedTasksCount()} of {this.senseChoiceGeneratedTasks.length} tasks selected
          (minimum {this.senseChoiceType === 'lite' ? '2' : '4'} required)
        </e-text>
      </div>

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.getSelectedTasksCount() < (this.senseChoiceType === 'lite' ? 2 : 4)}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  /* SensePriority Config 2 - Question and Requirements */
  SensePriorityConfig2: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Enter question <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="text"
        name="sensePriorityQuestion"
        placeholder="e.g. Which features are most important to you?"
        value={this.sensePriorityQuestion}
      ></e-input>
      <l-spacer value={2.5}></l-spacer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <l-row>
          <e-text>
            <strong>
              Options to evaluate <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer variant="horizontal" value={0.25}></l-spacer>
          <e-button variant="link" action="showPriorityItemInfo">
            <e-image src="../../../assets/icon/light/info-light.svg" width="1.2em"></e-image>
          </e-button>
        </l-row>
        <e-button variant="link" action="addSensePriorityItem">
          + Add Option
        </e-button>
      </div>
      <l-spacer value={0.5}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={1}></l-spacer>
      {this.sensePriorityItems.length === 0 && (
        <div class="empty-state">
          <e-text variant="footnote">No options added yet</e-text>
        </div>
      )}

      {this.sensePriorityItems.length > 0 && (
        <div class="priority-items-list">
          {this.sensePriorityItems.map((item, index) => (
            <p-priority-item item={JSON.stringify(item)} index={index}></p-priority-item>
          ))}
        </div>
      )}

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={
            !this.sensePriorityQuestion || this.sensePriorityItems.length === 0 ? true : false
          }
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  SenseQueryPreview: FunctionalComponent = () => (
    <c-card>
      <l-row justifyContent="flex-start">
        <e-image src="../../../assets/icon/light/gear-six-light.svg" width="2em"></e-image>
        <l-spacer variant="horizontal" value={0.5}></l-spacer>
        <e-text variant="heading">SenseQuery Settings</e-text>
      </l-row>{' '}
      <l-spacer value={1}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">QUESTION</e-text>
      <e-text>{this.senseQueryQuestion}</e-text>
      {this.senseQueryCategories.length > 0 && (
        <div>
          <l-spacer value={2}></l-spacer>
          <e-text variant="footnote">CATEGORIES</e-text>
          <l-spacer value={0.25}></l-spacer>
          <ul class="survey-preview-options-list">
            {this.senseQueryCategories.map((obj: any) => (
              <li>{obj.label}</li>
            ))}
          </ul>
        </div>
      )}
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">THANK YOU MESSAGE</e-text>
      <e-text>{this.surveyThankYouMessage}</e-text>
    </c-card>
  );

  SensePriorityPreview: FunctionalComponent = () => (
    <c-card>
      <l-row justifyContent="flex-start">
        <e-image src="../../../assets/icon/light/gear-six-light.svg" width="2em"></e-image>
        <l-spacer variant="horizontal" value={0.5}></l-spacer>
        <e-text variant="heading">SensePriority Settings</e-text>
      </l-row>
      <l-spacer value={1}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">QUESTION</e-text>
      <e-text>{this.sensePriorityQuestion}</e-text>
      <l-spacer value={2}></l-spacer>
      {this.sensePriorityItems.length > 0 && (
        <div>
          <l-spacer value={2}></l-spacer>
          <e-text variant="footnote">PRIORITY ITEMS</e-text>
          <l-spacer value={0.25}></l-spacer>
          <ul class="survey-preview-options-list">
            {this.sensePriorityItems.map((item: any) => (
              <li>{item.title}</li>
            ))}
          </ul>
        </div>
      )}
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">THANK YOU MESSAGE</e-text>
      <e-text>{this.surveyThankYouMessage}</e-text>
    </c-card>
  );

  /* SensePoll Wizard Steps */
  SensePollConfig1: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Enter poll question <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="text"
        name="sensePollQuestion"
        placeholder="e.g. How was the checkout experience?"
        value={this.sensePollQuestion}
      ></e-input>
      <l-spacer value={2.5}></l-spacer>
      <e-text>
        <strong>
          How many answers can respondents select? <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <l-row justifyContent="flex-start">
        <e-input
          type="radio"
          name="sensePollChoiceType"
          value="singleChoice"
          checked={this.sensePollChoiceType === 'singleChoice' ? true : false}
        >
          Single Answer
        </e-input>
        <l-spacer variant="horizontal" value={1}></l-spacer>
        <e-input
          type="radio"
          name="sensePollChoiceType"
          value="multiChoice"
          checked={this.sensePollChoiceType === 'multiChoice' ? true : false}
        >
          Multi Answers
        </e-input>
      </l-row>
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.sensePollQuestion && this.sensePollChoiceType ? false : true}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  SensePollConfig2: FunctionalComponent = () => (
    <div>
      <div class="adjustment-row">
        <e-text>
          <strong>
            Poll Answers <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <e-button variant="link" action="addSensePollChoice">
          + Add Answer
        </e-button>
      </div>
      <l-spacer value={0.5}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={1}></l-spacer>
      <p-list-with-delete
        name="deleteSensePollChoice"
        items={JSON.stringify(this.sensePollChoices)}
        emptyMessage="No answers added yet"
      ></p-list-with-delete>
      <l-spacer value={2.5}></l-spacer>
      {this.sensePollChoices.length > 0 && (
        <div>
          <l-spacer value={2.5}></l-spacer>
          <e-text>
            <strong>Ask follow-up question for the following answers</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <div class="gallery">
            {this.sensePollChoices.map((option: any) => (
              <e-input
                type="checkbox"
                value={option.value}
                name="sensePollFollowUpChoice"
                checked={this.checkExistenceInFollowUpChoices(option.value)}
                label={option.label}
              >
                {option.label}
              </e-input>
            ))}
          </div>
        </div>
      )}
      {this.sensePollFollowUpChoices.length > 0 && this.sensePollChoices.length > 0 && (
        <div>
          <l-spacer value={2.5}></l-spacer>
          <e-text>
            <strong>Enter follow-up question</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="sensePollFollowUpQuestion"
            placeholder="What would you like to know further?"
            value={this.sensePollFollowUpQuestion}
          ></e-input>
        </div>
      )}
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.pollSenseConfig2NextButton(
            this.sensePollChoices,
            this.sensePollFollowUpChoices,
            this.sensePollFollowUpQuestion,
          )}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  SensePollPreview: FunctionalComponent = () => (
    <c-card>
      <l-row justifyContent="flex-start">
        <e-image src="../../../assets/icon/light/gear-six-light.svg" width="2em"></e-image>
        <l-spacer variant="horizontal" value={0.5}></l-spacer>
        <e-text variant="heading">SensePoll Settings</e-text>
      </l-row>{' '}
      <l-spacer value={1}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">POLL QUESTION</e-text>
      <e-text>{this.sensePollQuestion}</e-text>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">ANSWER CHOICE TYPE</e-text>
      <e-text>
        {this.sensePollChoiceType === 'multiChoice' ? 'Multiple Choice' : 'Single Choice'}
      </e-text>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">ANSWER OPTIONS</e-text>
      <l-spacer value={0.25}></l-spacer>
      <ul class="survey-preview-options-list">
        {this.sensePollChoices.map((obj: any) => (
          <li>{obj.label}</li>
        ))}
      </ul>
      {this.sensePollFollowUpChoices.length > 0 && this.sensePollChoices.length > 0 && (
        <div>
          <l-spacer value={2}></l-spacer>
          <e-text variant="footnote">ASK ADDITIONAL DETAILS WHEN:</e-text>
          <l-spacer value={0.25}></l-spacer>
          <ul class="survey-preview-options-list">
            {this.sensePollFollowUpChoices.map((obj: any) => (
              <li>"{obj.label}" is selected</li>
            ))}
          </ul>
        </div>
      )}
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">THANK YOU MESSAGE</e-text>
      <e-text>{this.surveyThankYouMessage}</e-text>
    </c-card>
  );

  /* Core Wizard Steps */
  SurveyTypeStep: FunctionalComponent = () => (
    <article>
      <this.SurveyRadio
        label="SensePrice"
        description="Find the optimal price your customers are willing to pay"
        value="sensePrice"
        color="purple"
      ></this.SurveyRadio>
      <this.SurveyRadio
        label="SensePriority"
        description="Identify & prioritize customer requirements"
        value="sensePriority"
        color="teal"
      ></this.SurveyRadio>
      <this.SurveyRadio
        label="SenseChoice"
        description="Understand what truly matters to your customers"
        value="senseChoice"
        color="blue"
      ></this.SurveyRadio>
      <this.SurveyRadio
        label="SensePoll"
        description="Capture quick customer opinions and preferences"
        value="sensePoll"
        color="indigo"
      ></this.SurveyRadio>
      <this.SurveyRadio
        label="SenseQuery"
        description="Discover the questions your customers are asking"
        value="senseQuery"
        color="turquoise"
      ></this.SurveyRadio>
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <div></div>
        <e-button disabled={this.surveyType.length > 0 ? false : true} action="nextStep">
          Next
        </e-button>
      </div>
    </article>
  );

  SurveyBasicsStep: FunctionalComponent = () => (
    <article>
      <e-text>
        <strong>
          Survey Title <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="text"
        name="surveyTitle"
        placeholder={this.surveyTitlePlaceholder[this.surveyType]}
        value={this.surveyTitle}
      ></e-input>
      <l-spacer value={0.5}></l-spacer>
      <e-text variant="footnote">
        <e-link variant="externalLink" url="https://sensefolks.com">
          Read survey naming guide (1 min)
        </e-link>
      </e-text>
      <l-spacer value={2.5}></l-spacer>
      <e-text>
        <strong>
          How will you distribute this survey? <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="radio"
        name="surveyDistributionMode"
        value="embed"
        checked={this.surveyDistributionMode === 'embed' ? true : false}
      >
        Embed on a website
      </e-input>
      <l-spacer value={1}></l-spacer>
      <e-input
        type="radio"
        name="surveyDistributionMode"
        value="link"
        checked={this.surveyDistributionMode === 'link' ? true : false}
      >
        Share as a link{' '}
      </e-input>
      <l-spacer value={1}></l-spacer>
      <e-input
        type="radio"
        name="surveyDistributionMode"
        value="embedlink"
        checked={this.surveyDistributionMode === 'embedlink' ? true : false}
      >
        Both embed & share
      </e-input>
      {this.isEmbedMode && (
        <div>
          <l-spacer value={2.5}></l-spacer>
          <e-text>
            <strong>
              Enter URL where survey will be embedded <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <e-text variant="footnote">IMPORTANT: Only secure URLs are supported</e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="surveyEmbedUrl"
            placeholder={this.surveyEmbedPlaceholder[this.surveyType]}
            value={this.surveyEmbedUrl}
          ></e-input>
        </div>
      )}
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.basicStepNextButton(
            this.surveyTitle,
            this.surveyDistributionMode,
            this.surveyEmbedUrl,
          )}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </article>
  );

  SurveyConfigPart1Step: FunctionalComponent = () => (
    <article>
      {this.surveyType === 'sensePoll' && <this.SensePollConfig1></this.SensePollConfig1>}
      {this.surveyType === 'senseQuery' && <this.SenseQueryConfig1></this.SenseQueryConfig1>}
      {this.surveyType === 'sensePriority' && (
        <this.SensePriorityConfig1></this.SensePriorityConfig1>
      )}
      {this.surveyType === 'sensePrice' && <this.SensePriceConfig1></this.SensePriceConfig1>}
      {this.surveyType === 'senseChoice' && <this.SenseChoiceConfig1></this.SenseChoiceConfig1>}

      {/* Modal for category - SenseQuery specific */}
      {this.surveyType === 'senseQuery' && (
        <p-modal is-open={this.isCategoryModalOpen} modal-title="Add Category">
          {this.isCategoryModalOpen && <p-category-form></p-category-form>}
        </p-modal>
      )}

      {/* Modal for category information - SenseQuery specific */}
      {this.surveyType === 'senseQuery' && (
        <p-modal is-open={this.isCategoryInfoModalOpen} modal-title="Benefits of Categories">
          {this.isCategoryInfoModalOpen && (
            <div>
              <e-text>
                Respondents can choose a category that is relevant to their query e.g. Pricing,
                General, Support etc. This helps you to:
              </e-text>
              <l-spacer value={1}></l-spacer>
              <ul style={{ paddingLeft: '1.5em', margin: '0' }}>
                <li>
                  <e-text>
                    <strong>Group similar queries</strong> for easier analysis
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Spot unanswered topics</strong> in categories
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Identify areas</strong> that need most attention
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Present findings</strong> to different teams based on the categories
                  </e-text>
                </li>
              </ul>
              <l-spacer value={2}></l-spacer>
              <l-row justifyContent="space-between" direction="row-reverse">
                <e-button action="closeCategoryInfo">Ok</e-button>
              </l-row>
            </div>
          )}
        </p-modal>
      )}
    </article>
  );

  SurveyConfigPart2Step: FunctionalComponent = () => (
    <article>
      {this.surveyType === 'sensePoll' && <this.SensePollConfig2></this.SensePollConfig2>}
      {this.surveyType === 'senseQuery' && (
        <div>
          <e-text>
            <strong>SenseQuery configuration is complete!</strong>
          </e-text>
          <l-spacer value={1}></l-spacer>
          <e-text variant="footnote">
            Your query survey has been configured. Click Next to continue with respondent details.
          </e-text>
          <l-spacer value={3}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={1.5}></l-spacer>
          <div class="row">
            <e-button variant="light" action="prevStep">
              Back
            </e-button>
            <e-button action="nextStep">Next</e-button>
          </div>
        </div>
      )}
      {this.surveyType === 'sensePriority' && (
        <this.SensePriorityConfig2></this.SensePriorityConfig2>
      )}
      {this.surveyType === 'senseChoice' && this.senseChoiceConfigStep === 1 && (
        <this.SenseChoiceConfig2></this.SenseChoiceConfig2>
      )}
      {this.surveyType === 'senseChoice' && this.senseChoiceConfigStep === 2 && (
        <this.SenseChoiceConfig3></this.SenseChoiceConfig3>
      )}
      {this.surveyType === 'senseChoice' && this.senseChoiceConfigStep === 3 && (
        <this.SenseChoiceConfig4></this.SenseChoiceConfig4>
      )}
      {this.surveyType === 'senseChoice' && this.senseChoiceConfigStep === 4 && (
        <this.SenseChoiceConfig5></this.SenseChoiceConfig5>
      )}
      {this.surveyType === 'sensePrice' && <this.SensePriceConfig2></this.SensePriceConfig2>}

      {/* Modal for priority item - SensePriority specific */}
      {this.surveyType === 'sensePriority' && (
        <p-modal
          is-open={this.isPriorityItemModalOpen}
          modal-title={this.isEditingPriorityItem ? 'Edit Option' : 'Add Option'}
        >
          {this.isPriorityItemModalOpen && (
            <p-priority-item-form
              editing-item={
                this.editingPriorityItem ? JSON.stringify(this.editingPriorityItem) : ''
              }
              is-edit-mode={this.isEditingPriorityItem}
            ></p-priority-item-form>
          )}
        </p-modal>
      )}

      {/* Modal for priority items information - SensePriority specific */}
      {this.surveyType === 'sensePriority' && (
        <p-modal is-open={this.isPriorityItemInfoModalOpen} modal-title="About Options">
          {this.isPriorityItemInfoModalOpen && (
            <div>
              <e-text>
                Options are features, ideas, or improvements you are considering for your product or
                service.
              </e-text>
              <l-spacer value={2}></l-spacer>
              <e-text>For example:</e-text>
              <l-spacer value={0.5}></l-spacer>
              <ul style={{ paddingLeft: '1em', margin: '0' }}>
                <li>“Dark mode” for an app</li>
                <l-spacer value={0.5}></l-spacer>
                <li>“1:1 mentoring” for a course</li>
                <l-spacer value={0.5}></l-spacer>
                <li>“Video consultation” for a health service</li>
              </ul>
              <l-spacer value={2}></l-spacer>
              <e-text>
                If you have several ideas to improve your product or service, asking your customers
                to prioritize them will help you :
              </e-text>
              <l-spacer value={0.5}></l-spacer>
              <ul style={{ paddingLeft: '1em', margin: '0' }}>
                <li>
                  <e-text>
                    <strong>Understand what they care about most</strong>
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Make smarter decisions</strong> on what to improve
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Focus your time and resources</strong> on high-impact ideas{' '}
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Avoid wasting time</strong> on unnecessary features or ideas
                  </e-text>
                </li>
              </ul>
              <l-spacer value={2}></l-spacer>
              <l-row justifyContent="space-between" direction="row-reverse">
                <e-button action="closePriorityItemInfo">Ok</e-button>
              </l-row>
            </div>
          )}
        </p-modal>
      )}

      {/* Modal for poll choice - SensePoll specific */}
      {this.surveyType === 'sensePoll' && (
        <p-modal is-open={this.isPollChoiceModalOpen} modal-title="Add Answer">
          {this.isPollChoiceModalOpen && <p-poll-answer-form></p-poll-answer-form>}
        </p-modal>
      )}

      {/* Modal for attribute - SenseChoice specific */}
      {this.surveyType === 'senseChoice' && (
        <p-modal is-open={this.isAttributeModalOpen} modal-title="Add Attribute">
          {this.isAttributeModalOpen && <p-attribute-form></p-attribute-form>}
        </p-modal>
      )}

      {/* Modal for variant - SenseChoice specific */}
      {this.surveyType === 'senseChoice' && (
        <p-modal is-open={this.isVariantModalOpen} modal-title="Add Variant">
          {this.isVariantModalOpen && <p-variant-form></p-variant-form>}
        </p-modal>
      )}

      {/* Modal for variant info - SenseChoice specific */}
      {this.surveyType === 'senseChoice' && (
        <p-modal is-open={this.isVariantInfoModalOpen} modal-title="What are Variants?">
          {this.isVariantInfoModalOpen && (
            <div>
              <e-text>
                <strong>Variants</strong> are the options for each feature.
              </e-text>
              <l-spacer value={1}></l-spacer>
              <e-text>For example, if your attribute is "Price", your variants could be:</e-text>
              <l-spacer value={0.5}></l-spacer>
              <ul style={{ paddingLeft: '1.5em', margin: '0' }}>
                <li>
                  <e-text>Low ($299)</e-text>
                </li>
                <li>
                  <e-text>Medium ($599)</e-text>
                </li>
                <li>
                  <e-text>High ($899)</e-text>
                </li>
              </ul>
              <l-spacer value={1}></l-spacer>
              <e-text>
                Each attribute should have at least 2 variants to create meaningful comparisons.
              </e-text>
              <l-spacer value={1}></l-spacer>

              <div class="notice">
                <e-text variant="footnote">
                  <strong>💡 More Examples:</strong>
                </e-text>
                <l-spacer value={0.5}></l-spacer>
                <ul style={{ paddingLeft: '0', margin: '0', listStyle: 'none' }}>
                  <li style={{ display: 'inline' }}>
                    <e-text>
                      • <strong>Quality:</strong> Basic, Standard, Premium
                    </e-text>
                  </li>
                  <li style={{ display: 'inline' }}>
                    <e-text>
                      {' '}
                      • <strong>Speed:</strong> Slow, Fast, Very Fast
                    </e-text>
                  </li>
                  <li style={{ display: 'inline' }}>
                    <e-text>
                      {' '}
                      • <strong>Size:</strong> Small, Medium, Large
                    </e-text>
                  </li>
                </ul>
              </div>
              <l-spacer value={3}></l-spacer>
              <l-row justifyContent="flex-end">
                <e-button action="closeLevelInfo">Ok</e-button>
              </l-row>
            </div>
          )}
        </p-modal>
      )}
    </article>
  );

  SurveyRespondentDetailsStep: FunctionalComponent = () => (
    <article>
      <e-text>
        <strong>What details do you want to know about the respondents?</strong>
      </e-text>
      <l-spacer value={1}></l-spacer>

      {/* Dropdown selection with Create Custom Detail option */}
      <e-select
        name="surveyRespondentDetails"
        options={JSON.stringify(this.respondentDetails)}
        resetTrigger={this.resetRespondentDetailsSelectTrigger}
      ></e-select>
      <l-spacer value={2}></l-spacer>

      {/* Display selected respondent details */}
      {this.surveyRespondentDetails.length > 0 && (
        <div>
          <e-text variant="footnote">
            The following details will be collected from respondents
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          {this.surveyRespondentDetails.map((detail, index) => (
            <div>
              {index > 0 && <l-spacer value={2}></l-spacer>}
              <div class="respondent-detail-container">
                <c-card>
                  <l-row justifyContent="space-between" align="center">
                    <e-text>
                      <strong>{detail.label}</strong>
                      {detail.required && <span class="required-badge"> * </span>}
                    </e-text>
                    <div class="detail-actions">
                      <e-button variant="link" action={`editRespondentDetail-${index}`}>
                        <e-image
                          src="../../../assets/icon/dark/edit-dark.svg"
                          width="1.2em"
                        ></e-image>
                      </e-button>
                      <e-button variant="link" action={`deleteRespondentDetail-${index}`}>
                        <e-image
                          src="../../../assets/icon/red/trash-red.svg"
                          width="1.2em"
                        ></e-image>
                      </e-button>
                    </div>
                  </l-row>
                  <l-spacer value={2}></l-spacer>
                  <div class="detail-content">
                    <div class="detail-section">
                      <e-text variant="footnote" class="detail-label">
                        TYPE
                      </e-text>
                      <e-text>{detail.inputType}</e-text>
                    </div>

                    {detail.placeholder && (
                      <div>
                        <l-spacer value={2}></l-spacer>
                        <div class="detail-section">
                          <e-text variant="footnote" class="detail-label">
                            PLACEHOLDER
                          </e-text>
                          <e-text>{detail.placeholder}</e-text>
                        </div>
                      </div>
                    )}

                    {detail.options && detail.options.length > 0 && (
                      <div>
                        <l-spacer value={2}></l-spacer>
                        <div class="detail-section">
                          <e-text variant="footnote" class="detail-label">
                            OPTIONS
                          </e-text>
                          <ul class="options-list">
                            {detail.options.map(option => (
                              <li class="option-item">
                                <e-text>{option.label}</e-text>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}

                    {detail.defaultValue && (
                      <div>
                        <l-spacer value={2}></l-spacer>
                        <div class="detail-section">
                          <e-text variant="footnote" class="detail-label">
                            DEFAULT VALUE
                          </e-text>
                          <e-text>{detail.defaultValue}</e-text>
                        </div>
                      </div>
                    )}
                  </div>
                </c-card>
              </div>
            </div>
          ))}
          <l-spacer value={1}></l-spacer>
          <e-text variant="footnote">
            <span class="required-badge"> * </span> are required fields
          </e-text>
        </div>
      )}
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button disabled={false} action="nextStep">
          Next
        </e-button>
      </div>

      {/* Modal for custom respondent detail */}
      <p-modal
        is-open={this.isCustomDetailModalOpen}
        modal-title={this.isEditMode ? 'Edit Respondent Detail' : 'Create Respondent Detail'}
      >
        {this.isCustomDetailModalOpen && (
          <p-respondent-detail-form
            editing-detail={this.editingDetail ? JSON.stringify(this.editingDetail) : ''}
            is-edit-mode={this.isEditMode}
          ></p-respondent-detail-form>
        )}
      </p-modal>
    </article>
  );

  SurveyThankYouMessageStep: FunctionalComponent = () => (
    <article>
      <e-text>
        <strong>
          Thank You Message <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-text variant="footnote">
        This message will be shown to respondents after they submit their response
      </e-text>
      <l-spacer value={1}></l-spacer>
      <e-input
        type="text"
        name="surveyThankYouMessage"
        placeholder="e.g. Thank you for your feedback! We appreciate your time."
        value={this.surveyThankYouMessage}
      ></e-input>
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button disabled={this.surveyThankYouMessage.trim().length === 0} action="nextStep">
          Next
        </e-button>
      </div>
    </article>
  );

  SurveyPreviewStep: FunctionalComponent = () => (
    <article>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.surveyTitle.length > 0 ? false : true}
          action="createSurvey"
          active={this.isCreatingSurvey}
        >
          Confirm & Create
        </e-button>
      </div>
      <l-spacer value={2}></l-spacer>
      <c-card>
        <l-row justifyContent="flex-start">
          <e-image src="../../../assets/icon/light/info-light.svg" width="2em"></e-image>
          <l-spacer variant="horizontal" value={0.5}></l-spacer>
          <e-text variant="heading">Basic Info</e-text>
        </l-row>{' '}
        <l-spacer value={1}></l-spacer>
        <l-separator></l-separator>
        <l-spacer value={2}></l-spacer>
        <e-text variant="footnote">SURVEY TYPE</e-text>
        <e-text>{this.generateSurveyName(this.surveyType)}</e-text>
        <l-spacer value={2}></l-spacer>
        <e-text variant="footnote">SURVEY TITLE</e-text>
        <e-text>{this.surveyTitle}</e-text>
        <l-spacer value={2}></l-spacer>
        <e-text variant="footnote">DISTRIBUTION MODE</e-text>
        <e-text>
          {this.surveyDistributionMode === 'embed' && 'Embed survey into website or app'}
          {this.surveyDistributionMode === 'link' && 'Share survey as a link'}
          {this.surveyDistributionMode === 'embedlink' && 'Both, embed survey and share as a link'}
        </e-text>
        {this.surveyDistributionMode != 'link' && (
          <div>
            <l-spacer value={2}></l-spacer>
            <e-text variant="footnote">SURVEY EMBED LINK</e-text>
            <e-link variant="externalLink" url={this.surveyEmbedUrl}>
              {this.surveyEmbedUrl}
            </e-link>
          </div>
        )}
      </c-card>
      <l-spacer value={2}></l-spacer>

      {this.surveyType === 'sensePoll' && <this.SensePollPreview></this.SensePollPreview>}
      {this.surveyType === 'senseQuery' && <this.SenseQueryPreview></this.SenseQueryPreview>}
      {this.surveyType === 'sensePriority' && (
        <this.SensePriorityPreview></this.SensePriorityPreview>
      )}
      {this.surveyRespondentDetails.length > 0 && (
        <div>
          {' '}
          <l-spacer value={2}></l-spacer>
          <c-card>
            <l-row justifyContent="flex-start">
              <e-image src="../../../assets/icon/light/users-light.svg" width="2em"></e-image>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              <e-text variant="heading">Respondent Details</e-text>
            </l-row>{' '}
            <l-spacer value={1}></l-spacer>
            <l-separator></l-separator>
            <l-spacer value={2}></l-spacer>
            <e-text variant="footnote">DETAILS REQUESTED FROM RESPONDENTS</e-text>
            <l-spacer value={0.25}></l-spacer>
            <ul class="survey-preview-options-list">
              {this.surveyRespondentDetails.map((detail: any) => (
                <li>
                  {detail.label}
                  {detail.required && <span class="required-badge"> *</span>}
                  {detail.inputType === 'text' && <span class="detail-options"> - Text input</span>}
                  {detail.inputType === 'email' && (
                    <span class="detail-options"> - Email input</span>
                  )}
                  {detail.inputType === 'number' && (
                    <span class="detail-options"> - Number input</span>
                  )}
                  {detail.inputType === 'select' && detail.options && (
                    <span class="detail-options">
                      {' '}
                      - Dropdown with {detail.options.length} options
                    </span>
                  )}
                  {detail.inputType === 'radio' && detail.options && (
                    <span class="detail-options">
                      {' '}
                      - Radio buttons with {detail.options.length} options
                    </span>
                  )}
                  {detail.inputType === 'checkbox' && detail.options && (
                    <span class="detail-options">
                      {' '}
                      - Checkboxes with {detail.options.length} options
                    </span>
                  )}
                </li>
              ))}
            </ul>
            <l-spacer value={1}></l-spacer>
            <e-text variant="footnote">
              <span class="required-badge"> * </span> are required fields
            </e-text>
          </c-card>
        </div>
      )}
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.surveyTitle.length > 0 ? false : true}
          action="createSurvey"
          active={this.isCreatingSurvey}
        >
          Confirm & Create
        </e-button>
      </div>
    </article>
  );

  render() {
    return (
      <Host>
        <main>
          <l-row>
            <e-text variant="display">
              {this.surveyType.length === 0
                ? 'Create Survey'
                : `Create ${this.generateSurveyName(this.surveyType)}`}
            </e-text>
            <e-button action="closeWizard" variant="light">
              <e-image src="../../../assets/icon/red/x-red.svg" width="1.25em"></e-image>
            </e-button>
          </l-row>
          <e-text variant="footnote">
            STEP {this.getDisplayStepNumber()} OF {this.getDisplayTotalSteps()} -{' '}
            {this.currentStepIndex === 2 || this.currentStepIndex === 3
              ? `${this.generateSurveyName(this.surveyType).toUpperCase()} ${
                  this.surveyType === 'senseChoice' && this.currentStepIndex === 3
                    ? `CONFIGURATION (${this.senseChoiceConfigStep + 1})`
                    : this.surveySteps[this.currentStepIndex].label.toUpperCase()
                }`
              : this.surveySteps[this.currentStepIndex].label.toUpperCase()}
          </e-text>
          <l-spacer value={1.5}></l-spacer>
          <p-dotgrid width="100%" height="50px"></p-dotgrid>
          <l-spacer value={1.5}></l-spacer>
          {this.currentStepIndex === 0 && <this.SurveyTypeStep></this.SurveyTypeStep>}
          {this.currentStepIndex === 1 && <this.SurveyBasicsStep></this.SurveyBasicsStep>}
          {this.currentStepIndex === 2 && <this.SurveyConfigPart1Step></this.SurveyConfigPart1Step>}
          {this.currentStepIndex === 3 && <this.SurveyConfigPart2Step></this.SurveyConfigPart2Step>}
          {this.currentStepIndex === 4 && (
            <this.SurveyRespondentDetailsStep></this.SurveyRespondentDetailsStep>
          )}
          {this.currentStepIndex === 5 && (
            <this.SurveyThankYouMessageStep></this.SurveyThankYouMessageStep>
          )}
          {this.currentStepIndex === 6 && <this.SurveyPreviewStep></this.SurveyPreviewStep>}
        </main>
      </Host>
    );
  }
}
