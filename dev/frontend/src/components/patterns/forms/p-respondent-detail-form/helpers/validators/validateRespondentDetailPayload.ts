import Joi from 'joi';
import { RespondentDetailOption } from '../../../../../../global/script/var';

/**
 * Joi validation schema for respondent detail payload validation
 *
 * This schema defines the validation rules for respondent detail payloads
 * used in the p-respondent-detail-form component. It ensures data integrity
 * and provides comprehensive validation for all respondent detail fields.
 *
 * Validation Rules:
 * - Label: Required string, 1-100 characters, automatically trimmed
 * - InputType: Must be one of the accepted input types
 * - Placeholder: Required for text/email/number types, optional for others
 * - Options: Required array for select/radio/checkbox types, optional for others
 * - DefaultValue: Optional string field
 * - Required: Optional boolean field
 *
 * Security Features:
 * - Length limits prevent potential DoS attacks
 * - Input type validation ensures only accepted types are used
 * - Conditional validation based on input type
 */

const validateRespondentDetailPayloadSchema = Joi.object({
  label: Joi.string().trim().min(1).max(100).required(),
  inputType: Joi.string()
    .valid('text', 'email', 'select', 'radio', 'checkbox', 'number')
    .required(),
  placeholder: Joi.when('inputType', {
    is: Joi.string().valid('text', 'email', 'number'),
    then: Joi.string().trim().min(1).max(200).required(),
    otherwise: Joi.string().optional().allow(''),
  }),
  options: Joi.when('inputType', {
    is: Joi.string().valid('select', 'radio', 'checkbox'),
    then: Joi.array()
      .items(
        Joi.object({
          value: Joi.string().required(),
          label: Joi.string().required(),
        }),
      )
      .min(1)
      .required(),
    otherwise: Joi.array().optional(),
  }),
  defaultValue: Joi.string().optional().allow(''),
  required: Joi.boolean().optional(),
});

/**
 * Validates a respondent detail payload against the defined schema
 *
 * This function performs comprehensive validation of respondent detail payloads
 * before they are processed. It ensures data integrity and provides detailed
 * error information for validation failures.
 *
 * Validation Process:
 * 1. Checks label format and length constraints
 * 2. Validates input type against accepted values
 * 3. Conditionally validates placeholder based on input type
 * 4. Conditionally validates options array for select/radio/checkbox types
 * 5. Returns detailed error messages for user feedback
 *
 * @param payload - The respondent detail payload to validate
 * @returns Validation result with success status and error details
 *
 * @example
 * // Valid text input payload
 * const result = validateRespondentDetailPayload({
 *   label: 'Full Name',
 *   inputType: 'text',
 *   placeholder: 'Enter your full name',
 *   required: true
 * });
 * // Returns: { isValid: true, validationMessage: '', errors: {} }
 *
 * @example
 * // Valid select input payload
 * const result = validateRespondentDetailPayload({
 *   label: 'Age Range',
 *   inputType: 'select',
 *   options: [
 *     { value: '18_24', label: '18-24' },
 *     { value: '25_34', label: '25-34' }
 *   ],
 *   required: true
 * });
 * // Returns: { isValid: true, validationMessage: '', errors: {} }
 *
 * @example
 * // Invalid payload - missing required placeholder
 * const result = validateRespondentDetailPayload({
 *   label: 'Email',
 *   inputType: 'email',
 *   required: true
 * });
 * // Returns: { isValid: false, validationMessage: '❌ "placeholder" is required', errors: {...} }
 */

export const validateRespondentDetailPayload = (payload: Partial<RespondentDetailOption>) => {
  // Perform Joi validation against the schema with detailed error reporting
  const { error } = validateRespondentDetailPayloadSchema.validate(payload, {
    abortEarly: false,
  });

  if (error) {
    // Create error mapping for field-specific error handling
    const errors: { [key: string]: string } = {};
    for (const detail of error.details) {
      const path = detail.path[0] as string;
      errors[path] = detail.message;
    }

    // Return user-friendly error message with emoji for visual clarity
    return {
      isValid: false,
      validationMessage: `❌ ${error.details[0].message}`,
      errors,
    };
  } else {
    // Validation successful
    return {
      isValid: true,
      validationMessage: '',
      errors: {},
    };
  }
};
