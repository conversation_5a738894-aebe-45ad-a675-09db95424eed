import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';
import { surveyModel } from '../../../survey/models';

/**
 * Middleware to validate survey type and key consistency before saving response
 * This ensures the survey data from cache/middleware matches the database
 * and validates the survey is in a valid state for accepting responses
 */
export const validateSurveyConsistencyForResponse = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const survey = res.locals.survey;
    const { surveyPublicKey } = res.locals.validatedPayload;

    if (!survey) {
      console.log(`${Var.app.emoji.failure} Survey not found in res.locals`);
      return res.status(404).json({
        success: false,
        message: 'Survey not found',
      });
    }

    if (!surveyPublicKey) {
      console.log(`${Var.app.emoji.failure} Survey public key missing from validated payload`);
      return res.status(400).json({
        success: false,
        message: 'Survey public key is required',
      });
    }

    // Fetch the survey from database to ensure consistency
    const dbSurvey = await surveyModel.findOne({
      attributes: ['id', 'account_id', 'public_key', 'title', 'type', 'distribution', 'response_count', 'is_deleted', 'is_disabled'],
      where: { public_key: surveyPublicKey },
    });

    if (!dbSurvey) {
      console.log(`${Var.app.emoji.failure} Survey not found in database for public key: ${surveyPublicKey}`);
      return res.status(404).json({
        success: false,
        message: 'Survey not found',
      });
    }

    // Validate that the survey type matches between cache and database
    if (survey.type !== dbSurvey.get('type')) {
      console.log(`${Var.app.emoji.failure} Survey type mismatch. Cache: ${survey.type}, DB: ${dbSurvey.get('type')}`);
      return res.status(400).json({
        success: false,
        message: 'Survey validation failed - type mismatch',
      });
    }

    // Validate that the public key matches between cache and database
    if (survey.public_key !== dbSurvey.get('public_key')) {
      console.log(`${Var.app.emoji.failure} Survey public key mismatch. Cache: ${survey.public_key}, DB: ${dbSurvey.get('public_key')}`);
      return res.status(400).json({
        success: false,
        message: 'Survey validation failed - key mismatch',
      });
    }

    // Validate that the requested public key matches the database
    if (surveyPublicKey !== dbSurvey.get('public_key')) {
      console.log(`${Var.app.emoji.failure} Requested public key mismatch. Requested: ${surveyPublicKey}, DB: ${dbSurvey.get('public_key')}`);
      return res.status(400).json({
        success: false,
        message: 'Survey validation failed - request key mismatch',
      });
    }

    // Validate survey is not deleted
    if (dbSurvey.get('is_deleted')) {
      console.log(`${Var.app.emoji.failure} Survey is deleted: ${surveyPublicKey}`);
      return res.status(404).json({
        success: false,
        message: 'Survey not found',
      });
    }

    // Validate survey is not disabled
    if (dbSurvey.get('is_disabled')) {
      console.log(`${Var.app.emoji.failure} Survey is disabled: ${surveyPublicKey}`);
      return res.status(403).json({
        success: false,
        message: 'Survey is not available for responses',
      });
    }

    // Validate survey type is allowed for responses
    const allowedTypes = ['sensePrice', 'senseChoice', 'sensePoll', 'senseQuery', 'sensePriority'];
    const surveyType = dbSurvey.get('type') as string;
    if (!allowedTypes.includes(surveyType)) {
      console.log(`${Var.app.emoji.failure} Survey type not allowed for responses: ${surveyType}`);
      return res.status(400).json({
        success: false,
        message: 'Survey type is not valid for responses',
      });
    }

    // Validate survey has required fields for response submission
    if (!dbSurvey.get('id') || !dbSurvey.get('account_id')) {
      console.log(`${Var.app.emoji.failure} Survey missing required fields for response submission`);
      return res.status(500).json({
        success: false,
        message: 'Survey data is incomplete',
      });
    }

    // Store validated database survey data for use in controller
    res.locals.dbSurvey = dbSurvey;
    res.locals.surveyId = dbSurvey.get('id');
    res.locals.accountId = dbSurvey.get('account_id');

    // Log successful validation in development
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Survey consistency validation passed for response submission: ${surveyPublicKey}`);
      console.log(`${Var.app.emoji.success} Survey ID: ${dbSurvey.get('id')}, Type: ${dbSurvey.get('type')}`);
    }

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in validateSurveyConsistencyForResponse:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error validating survey for response submission',
    });
  }
};
