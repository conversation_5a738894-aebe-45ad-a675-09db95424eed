import { Request, Response, NextFunction } from 'express';
import { readSurveyByPublicKey } from '../../dals';
import { getSurveyFromCache, setSurveyInCache } from '../../../../global/services';
import { Var } from '../../../../global/var';

export const verifySurveyExists = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { surveyPublicKey } = res.locals.validatedPayload;

    if (!surveyPublicKey) {
      return res.status(400).json({
        success: false,
        message: 'Survey public key is required',
      });
    }

    let survey = await getSurveyFromCache(surveyPublicKey);
    let fromCache = false;

    if (survey) {
      fromCache = true;
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} Survey found in cache: ${surveyPublicKey}`);
      }
    } else {
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.warning} Survey not in cache, fetching from database: ${surveyPublicKey}`);
      }

      survey = await readSurveyByPublicKey(surveyPublicKey);

      if (survey) {
        await setSurveyInCache(surveyPublicKey, survey);
        if (Var.node.env === 'dev') {
          console.log(`${Var.app.emoji.success} Survey cached: ${surveyPublicKey}`);
        }
      }
    }

    if (!survey) {
      console.log(`${Var.app.emoji.failure} Survey not found: ${surveyPublicKey}`);
      return res.status(404).json({
        success: false,
        message: 'Survey not found',
      });
    }

    // Ensure survey has a public_key property
    if (!survey.public_key) {
      console.error(`${Var.app.emoji.failure} Survey found but missing public_key: ${surveyPublicKey}`);
      return res.status(500).json({
        success: false,
        message: 'Survey data is invalid',
      });
    }

    // Validate survey type is one of the allowed types
    const allowedTypes = ['sensePrice', 'senseChoice', 'sensePoll', 'senseQuery', 'sensePriority'];
    if (!survey.type || !allowedTypes.includes(survey.type)) {
      console.error(`${Var.app.emoji.failure} Survey has invalid type: ${survey.type}`);
      return res.status(400).json({
        success: false,
        message: 'Survey type is invalid',
      });
    }

    // Validate survey is not deleted or disabled
    if (survey.is_deleted) {
      console.log(`${Var.app.emoji.failure} Survey is deleted: ${surveyPublicKey}`);
      return res.status(404).json({
        success: false,
        message: 'Survey not found',
      });
    }

    if (survey.is_disabled) {
      console.log(`${Var.app.emoji.failure} Survey is disabled: ${surveyPublicKey}`);
      return res.status(403).json({
        success: false,
        message: 'Survey is not available',
      });
    }

    // Validate survey public key matches the requested key
    if (survey.public_key !== surveyPublicKey) {
      console.error(`${Var.app.emoji.failure} Survey public key mismatch. Expected: ${surveyPublicKey}, Found: ${survey.public_key}`);
      return res.status(400).json({
        success: false,
        message: 'Survey validation failed',
      });
    }

    res.locals.survey = survey;
    res.locals.fromCache = fromCache;

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Survey found: ${surveyPublicKey}`);
      console.log(`${Var.app.emoji.success} Survey object:`, survey);
    }

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in verifySurveyExists:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error verifying survey',
    });
  }
};
