import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';

const submitResponsePayloadSchema = Joi.object({
  surveyPublicKey: Joi.string().uuid().required().messages({
    'string.empty': 'Survey public key cannot be empty',
    'string.uuid': 'Survey public key must be a valid UUID',
    'any.required': 'Survey public key is required',
  }),
  responseData: Joi.object().required().messages({
    'object.base': 'Response data must be an object',
    'any.required': 'Response data is required',
  }),
  respondentDetails: Joi.object().optional().messages({
    'object.base': 'Respondent details must be an object',
  }),
  userAgent: Joi.object().required().messages({
    'object.base': 'User agent must be an object',
  }),
  completionTime: Joi.number().min(0).required().messages({
    'number.base': 'Completion time must be a number',
    'number.min': 'Completion time cannot be negative',
  }),
});

export const validateSubmitResponsePayload = (req: Request, res: Response, next: NextFunction) => {
  try {
    const payload = req.body;
    const { error } = submitResponsePayloadSchema.validate(payload, {
      abortEarly: false,
      allowUnknown: true,
    });
    if (error) {
      console.log(`${Var.app.emoji.failure} Invalid response payload:`, error.details);
      return res.status(400).json({
        success: false,
        message: 'Invalid response payload',
        details: error.details.map(detail => detail.message),
      });
    }
    // Ensure surveyPublicKey is a string
    if (typeof payload.surveyPublicKey !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Invalid response payload',
        details: ['Survey public key must be a string'],
      });
    }

    res.locals.validatedPayload = payload;

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Valid response payload for survey: ${payload.surveyPublicKey}`);
      console.log(`${Var.app.emoji.success} Validated payload:`, res.locals.validatedPayload);
    }

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in validateSubmitResponsePayload:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error validating request',
    });
  }
};
