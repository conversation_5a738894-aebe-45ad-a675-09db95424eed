/**
 * Generates a URL-friendly slug from a survey title
 * @param title The survey title to convert to a slug
 * @returns A URL-friendly slug string
 */
export const generateSurveySlug = (title: string): string => {
  if (!title || typeof title !== 'string') {
    return '';
  }

  return title
    .trim()
    .toLowerCase()
    // Replace spaces and special characters with hyphens
    .replace(/[^a-z0-9]+/g, '-')
    // Remove leading and trailing hyphens
    .replace(/^-+|-+$/g, '')
    // Replace multiple consecutive hyphens with a single hyphen
    .replace(/-+/g, '-')
    // Limit length to 50 characters for URL friendliness
    .substring(0, 50)
    // Remove trailing hyphen if it was created by substring
    .replace(/-+$/, '');
};
