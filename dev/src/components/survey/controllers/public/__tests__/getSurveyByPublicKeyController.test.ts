import { Request, Response } from 'express';
import { getSurveyByPublicKeyController } from '../getSurveyByPublicKeyController';
import { Var } from '../../../../../global/var';

// Mock the Var module
jest.mock('../../../../../global/var', () => ({
  Var: {
    app: {
      emoji: {
        success: '✅',
        failure: '❌',
      },
    },
  },
}));

// Mock the generateSurveySlug helper
jest.mock('../../helpers', () => ({
  generateSurveySlug: jest.fn((title: string) => {
    return title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
  }),
}));

describe('getSurveyByPublicKeyController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {
      locals: {},
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Successful survey retrieval', () => {
    it('should return survey data with generated slug', async () => {
      const mockSurvey = {
        title: 'Customer Satisfaction Survey',
        config: {
          question: 'How satisfied are you?',
          choices: ['Very satisfied', 'Satisfied', 'Neutral', 'Dissatisfied', 'Very dissatisfied'],
        },
        respondent_details: [
          { field: 'name', required: true },
          { field: 'email', required: false },
        ],
        type: 'sensePoll',
        public_key: '123e4567-e89b-12d3-a456-************',
      };

      mockResponse.locals!.survey = mockSurvey;

      await getSurveyByPublicKeyController(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: `${Var.app.emoji.success} Survey fetched by public key`,
        payload: {
          config: mockSurvey.config,
          respondentDetails: mockSurvey.respondent_details,
          type: mockSurvey.type,
          publicKey: mockSurvey.public_key,
          title: mockSurvey.title,
          slug: 'customer-satisfaction-survey',
        },
      });
    });

    it('should handle survey with minimal data', async () => {
      const mockSurvey = {
        title: 'Simple Survey',
        config: {},
        respondent_details: [],
        type: 'senseQuery',
        public_key: '987fcdeb-51a2-43d1-9f12-123456789abc',
      };

      mockResponse.locals!.survey = mockSurvey;

      await getSurveyByPublicKeyController(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: `${Var.app.emoji.success} Survey fetched by public key`,
        payload: {
          config: {},
          respondentDetails: [],
          type: 'senseQuery',
          publicKey: '987fcdeb-51a2-43d1-9f12-123456789abc',
          title: 'Simple Survey',
          slug: 'simple-survey',
        },
      });
    });

    it('should handle survey with complex title', async () => {
      const mockSurvey = {
        title: 'Product Feedback Survey 2024 (Q1) - Customer Experience!',
        config: { question: 'Rate our product' },
        respondent_details: [],
        type: 'sensePrice',
        public_key: '456e7890-e12b-34d5-a678-901234567890',
      };

      mockResponse.locals!.survey = mockSurvey;

      await getSurveyByPublicKeyController(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: `${Var.app.emoji.success} Survey fetched by public key`,
        payload: {
          config: { question: 'Rate our product' },
          respondentDetails: [],
          type: 'sensePrice',
          publicKey: '456e7890-e12b-34d5-a678-901234567890',
          title: 'Product Feedback Survey 2024 (Q1) - Customer Experience!',
          slug: 'product-feedback-survey-2024-q1-customer-experience',
        },
      });
    });

    it('should handle survey with undefined respondent_details', async () => {
      const mockSurvey = {
        title: 'Survey Without Respondent Details',
        config: { question: 'Test question' },
        type: 'sensePoll',
        public_key: '789e0123-e45b-67d8-a901-234567890123',
        // respondent_details is undefined
      };

      mockResponse.locals!.survey = mockSurvey;

      await getSurveyByPublicKeyController(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: `${Var.app.emoji.success} Survey fetched by public key`,
        payload: {
          config: { question: 'Test question' },
          respondentDetails: undefined,
          type: 'sensePoll',
          publicKey: '789e0123-e45b-67d8-a901-234567890123',
          title: 'Survey Without Respondent Details',
          slug: 'survey-without-respondent-details',
        },
      });
    });
  });

  describe('Edge cases', () => {
    it('should handle survey with empty title', async () => {
      const mockSurvey = {
        title: '',
        config: { question: 'Test question' },
        respondent_details: [],
        type: 'sensePoll',
        public_key: '123e4567-e89b-12d3-a456-************',
      };

      mockResponse.locals!.survey = mockSurvey;

      await getSurveyByPublicKeyController(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: `${Var.app.emoji.success} Survey fetched by public key`,
        payload: {
          config: { question: 'Test question' },
          respondentDetails: [],
          type: 'sensePoll',
          publicKey: '123e4567-e89b-12d3-a456-************',
          title: '',
          slug: '',
        },
      });
    });

    it('should handle survey with null title', async () => {
      const mockSurvey = {
        title: null,
        config: { question: 'Test question' },
        respondent_details: [],
        type: 'sensePoll',
        public_key: '123e4567-e89b-12d3-a456-************',
      };

      mockResponse.locals!.survey = mockSurvey;

      await getSurveyByPublicKeyController(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: `${Var.app.emoji.success} Survey fetched by public key`,
        payload: {
          config: { question: 'Test question' },
          respondentDetails: [],
          type: 'sensePoll',
          publicKey: '123e4567-e89b-12d3-a456-************',
          title: null,
          slug: '',
        },
      });
    });

    it('should handle survey with special characters in title', async () => {
      const mockSurvey = {
        title: 'Survey@#$%^&*()Title!!!',
        config: { question: 'Test question' },
        respondent_details: [],
        type: 'sensePoll',
        public_key: '123e4567-e89b-12d3-a456-************',
      };

      mockResponse.locals!.survey = mockSurvey;

      await getSurveyByPublicKeyController(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: `${Var.app.emoji.success} Survey fetched by public key`,
        payload: {
          config: { question: 'Test question' },
          respondentDetails: [],
          type: 'sensePoll',
          publicKey: '123e4567-e89b-12d3-a456-************',
          title: 'Survey@#$%^&*()Title!!!',
          slug: 'survey-title',
        },
      });
    });
  });

  describe('Different survey types', () => {
    const surveyTypes = ['sensePoll', 'senseQuery', 'sensePrice', 'senseChoice', 'sensePriority'];

    surveyTypes.forEach(type => {
      it(`should handle ${type} survey type`, async () => {
        const mockSurvey = {
          title: `${type} Survey`,
          config: { question: 'Test question' },
          respondent_details: [],
          type,
          public_key: '123e4567-e89b-12d3-a456-************',
        };

        mockResponse.locals!.survey = mockSurvey;

        await getSurveyByPublicKeyController(
          mockRequest as Request,
          mockResponse as Response
        );

        expect(mockResponse.status).toHaveBeenCalledWith(200);
        expect(mockResponse.json).toHaveBeenCalledWith({
          success: true,
          message: `${Var.app.emoji.success} Survey fetched by public key`,
          payload: {
            config: { question: 'Test question' },
            respondentDetails: [],
            type,
            publicKey: '123e4567-e89b-12d3-a456-************',
            title: `${type} Survey`,
            slug: `${type.toLowerCase()}-survey`,
          },
        });
      });
    });
  });

  describe('Response structure validation', () => {
    it('should always include all required fields in response', async () => {
      const mockSurvey = {
        title: 'Complete Survey',
        config: { question: 'Test question', choices: ['A', 'B', 'C'] },
        respondent_details: [{ field: 'email', required: true }],
        type: 'sensePoll',
        public_key: '123e4567-e89b-12d3-a456-************',
        // Additional fields that should not be included in response
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        account_id: 'some-account-id',
      };

      mockResponse.locals!.survey = mockSurvey;

      await getSurveyByPublicKeyController(
        mockRequest as Request,
        mockResponse as Response
      );

      const responseCall = (mockResponse.json as jest.Mock).mock.calls[0][0];
      const payload = responseCall.payload;

      // Check that all required fields are present
      expect(payload).toHaveProperty('config');
      expect(payload).toHaveProperty('respondentDetails');
      expect(payload).toHaveProperty('type');
      expect(payload).toHaveProperty('publicKey');
      expect(payload).toHaveProperty('title');
      expect(payload).toHaveProperty('slug');

      // Check that sensitive fields are not included
      expect(payload).not.toHaveProperty('created_at');
      expect(payload).not.toHaveProperty('updated_at');
      expect(payload).not.toHaveProperty('account_id');
    });
  });
});
