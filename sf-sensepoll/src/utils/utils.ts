/**
 * Utility functions for sf-sensepoll
 */

/**
 * Check if a survey key is valid UUID
 * @param key The survey key to validate
 * @returns True if the key is a valid UUID, false otherwise
 */
export function isValidKey(key: string | undefined | null): boolean {
  if (typeof key !== 'string' || key.trim().length === 0) {
    return false;
  }

  // UUID v4 regex pattern
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(key.trim());
}

/**
 * Format error messages for display
 * @param error The error object or message
 * @returns A formatted error message string
 */
export function formatErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'An unknown error occurred';
}
