# sf-sensepoll

A customizable, embeddable polling/voting web component that collects user preferences through a multi-step wizard interface.

## Overview

The `sf-sensepoll` component provides a frictionless polling experience that can be embedded on any website. It guides users through poll questions, optional follow-up questions, and optional respondent details collection.

## Installation

### NPM

```bash
npm install @sensefolks/sf-sensepoll --save
```

### Script Tag

```html
<script type="module" src="https://unpkg.com/@sensefolks/sf-sensepoll/dist/sf-sensepoll/sf-sensepoll.esm.js"></script>
<script nomodule src="https://unpkg.com/@sensefolks/sf-sensepoll/dist/sf-sensepoll/sf-sensepoll.js"></script>
```

## Basic Usage

Add the component to your HTML with a survey key:

```html
<sf-sensepoll survey-key="your-survey-key"></sf-sensepoll>
```

## Features

- **Single or Multiple Choice**: Support for both single-select (radio) and multi-select (checkbox) polling
- **Follow-up Questions**: Optional follow-up questions with additional choices
- **Respondent Details**: Optional collection of respondent information
- **Multi-step Wizard**: Smooth progression through poll steps
- **Validation**: Built-in validation for required fields
- **Error Handling**: Graceful error handling with retry functionality
- **Framework Agnostic**: Works with React, Vue, Angular, and vanilla JavaScript

## Configuration

The component expects a survey configuration from the API with the following structure:

```typescript
interface SurveyConfig {
  question: string; // Main poll question
  choiceType: string; // 'single' or 'multiple'
  choices: string[]; // Array of choice options
  followUpChoices?: string[]; // Optional follow-up choices
  followUpQuestion?: string; // Optional follow-up question
  thankYouMessage: string; // Thank you message
}
```

## Styling

The component is completely unstyled by default and uses CSS parts for customization. This allows you to match your brand's design system while maintaining the component's encapsulation.

### Example Styling

```css
/* Main container styling */
sf-sensepoll {
  display: block;
  font-family: system-ui, -apple-system, sans-serif;
  max-width: 600px;
  margin: 0 auto;
  color: #333;
}

/* Headings */
sf-sensepoll::part(heading) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

/* Choice options */
sf-sensepoll::part(choice-option) {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  cursor: pointer;
}

sf-sensepoll::part(choice-label) {
  margin-left: 0.5rem;
  font-size: 1rem;
}

/* Buttons */
sf-sensepoll::part(button) {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

sf-sensepoll::part(next-button),
sf-sensepoll::part(submit-button) {
  background-color: #3b82f6;
  color: white;
  border: none;
}

sf-sensepoll::part(back-button) {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  margin-right: 0.5rem;
}

/* Error states */
sf-sensepoll::part(error-message) {
  color: #ef4444;
  font-size: 0.875rem;
}

sf-sensepoll::part(loading-message) {
  color: #6b7280;
  font-style: italic;
}
```

## Framework Integration

### React

```jsx
import React from 'react';
import '@sensefolks/sf-sensepoll';

function PollComponent() {
  return <sf-sensepoll survey-key="your-survey-key"></sf-sensepoll>;
}
```

### Vue

```html
<template>
  <sf-sensepoll survey-key="your-survey-key"></sf-sensepoll>
</template>

<script>
  import '@sensefolks/sf-sensepoll';

  export default {
    name: 'PollComponent',
  };
</script>
```

### Angular

```typescript
// In your module
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {}

// In your component
import '@sensefolks/sf-sensepoll';
```

<!-- Auto Generated Below -->


## Properties

| Property    | Attribute    | Description | Type     | Default     |
| ----------- | ------------ | ----------- | -------- | ----------- |
| `surveyKey` | `survey-key` |             | `string` | `undefined` |


## Shadow Parts

| Part                           | Description |
| ------------------------------ | ----------- |
| `"back-button"`                |             |
| `"button"`                     |             |
| `"button-container"`           |             |
| `"checkbox-input"`             |             |
| `"choice-label"`               |             |
| `"choice-option"`              |             |
| `"choices-container"`          |             |
| `"error-container"`            |             |
| `"error-message"`              |             |
| `"follow-up-heading"`          |             |
| `"follow-up-step"`             |             |
| `"form-container"`             |             |
| `"form-field"`                 |             |
| `"form-input"`                 |             |
| `"form-label"`                 |             |
| `"heading"`                    |             |
| `"input"`                      |             |
| `"loading-message"`            |             |
| `"message"`                    |             |
| `"next-button"`                |             |
| `"poll-heading"`               |             |
| `"poll-step"`                  |             |
| `"required-indicator"`         |             |
| `"respondent-details-heading"` |             |
| `"respondent-details-step"`    |             |
| `"retry-button"`               |             |
| `"step"`                       |             |
| `"submit-button"`              |             |
| `"survey-container"`           |             |
| `"thank-you-heading"`          |             |
| `"thank-you-step"`             |             |


----------------------------------------------

*Built with [StencilJS](https://stenciljs.com/)*
