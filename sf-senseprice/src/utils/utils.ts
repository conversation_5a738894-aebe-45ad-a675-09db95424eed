/**
 * Utility functions for sf-senseprice
 */

/**
 * Check if a survey key is valid
 * @param key The survey key to validate
 * @returns True if the key is valid, false otherwise
 */
export function isValidKey(key: string | undefined | null): boolean {
  return typeof key === 'string' && key.trim().length > 0;
}

/**
 * Format error messages for display
 * @param error The error object or message
 * @returns A formatted error message string
 */
export function formatErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'An unknown error occurred';
}

/**
 * Format currency values for display
 * @param amount The amount to format
 * @param currency The currency code (e.g., 'USD', 'EUR')
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string): string {
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  } catch (error) {
    // Fallback if currency is not supported
    return `${currency.toUpperCase()} ${amount.toFixed(2)}`;
  }
}

/**
 * Validate if a price value is valid
 * @param price The price to validate
 * @returns True if the price is valid, false otherwise
 */
export function isValidPrice(price: string | number): boolean {
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  return !isNaN(numPrice) && numPrice >= 0;
}
